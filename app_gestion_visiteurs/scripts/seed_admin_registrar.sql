-- Seed initial admin and registrar users

-- Insert admin user
INSERT INTO users (id, username, email, password_hash, role, is_active, password_changed_at, created_at, updated_at)
VALUES (
    gen_random_uuid(),
    'admin',
    '<EMAIL>',
    crypt('AdminPassword123!', gen_salt('bf')),
    'admin',
    true,
    NOW(),
    NOW(),
    NOW()
);

-- Insert registrar user
INSERT INTO users (id, username, email, password_hash, role, is_active, password_changed_at, created_at, updated_at)
VALUES (
    gen_random_uuid(),
    'registrar1',
    '<EMAIL>',
    crypt('RegistrarPassword123!', gen_salt('bf')),
    'operator',
    true,
    NOW(),
    NOW(),
    NOW()
);
