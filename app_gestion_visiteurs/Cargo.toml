[package]
name = "app_gestion_visiteurs"
version = "0.1.0"
edition = "2021"

[dependencies]
actix-web = { version = "4.4", features = ["rustls"] }
actix-files = "0.6"
actix-session = { version = "0.8", features = ["cookie-session"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
jsonwebtoken = "9.2"
argon2 = "0.5"
chrono = { version = "0.4", features = ["serde"] }
dotenv = "0.15"
env_logger = "0.10"
tokio = { version = "1.0", features = ["full"] }
futures-util = "0.3"

# Dépendances HTTPS sécurisées
rustls = "0.21"
rustls-pemfile = "1.0"
rcgen = "0.11"

# Dépendances de sécurité
uuid = { version = "1.0", features = ["v4", "serde"] }
regex = "1.10"
base64 = "0.21"
rand = "0.8"
sha2 = "0.10"
hmac = "0.12"
log = "0.4"
aes-gcm = "0.10"
hex = "0.4"

# Base de données sécurisée PostgreSQL avec SQLx
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "chrono", "uuid", "json", "migrate"] }
anyhow = "1.0"
thiserror = "1.0"

# Nouvelles dépendances pour sécurité avancée (sans OpenSSL)
time = "0.3"
dashmap = "5.5"
governor = "0.6"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
tracing-actix-web = "0.7"
metrics = "0.22"
metrics-util = "0.16"
# Désactivé temporairement pour éviter OpenSSL
# metrics-exporter-prometheus = "0.13"
# redis = { version = "0.24", features = ["tokio-comp"] }
actix-cors = "0.7"
bcrypt = "0.15"
