//! Module de gestion de base de données sécurisée
//! Implémentation PostgreSQL avec SQLx et sécurité renforcée

pub mod connection;
pub mod migrations;
pub mod models;
pub mod repositories;
pub mod security;

use anyhow::Result;
use std::time::Duration;

/// Configuration de la base de données sécurisée
#[derive(Debug, Clone)]
pub struct DatabaseConfig {
    pub url: String,
    pub max_connections: u32,
    pub min_connections: u32,
    pub acquire_timeout: Duration,
    pub idle_timeout: Duration,
    pub max_lifetime: Duration,
    pub ssl_mode: String,
    pub application_name: String,
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            url: std::env::var("DATABASE_URL")
                .unwrap_or_else(|_| "postgresql://localhost/app_gestion_visiteurs".to_string()),
            max_connections: 20,
            min_connections: 5,
            acquire_timeout: Duration::from_secs(30),
            idle_timeout: Duration::from_secs(600),
            max_lifetime: Duration::from_secs(1800),
            ssl_mode: "require".to_string(),
            application_name: "app_gestion_visiteurs".to_string(),
        }
    }
}

/// Pool de connexions sécurisé
pub type DatabasePool = PgPool;

/// Initialise la base de données avec sécurité renforcée
pub async fn initialize_database(config: DatabaseConfig) -> Result<DatabasePool> {
    log::info!("Initialisation de la base de données PostgreSQL sécurisée");

    let pool = sqlx::postgres::PgPoolOptions::new()
        .max_connections(config.max_connections)
        .min_connections(config.min_connections)
        .acquire_timeout(config.acquire_timeout)
        .idle_timeout(config.idle_timeout)
        .max_lifetime(config.max_lifetime)
        .connect(&config.url)
        .await?;

    // Vérification de la connexion
    let row: (i64,) = sqlx::query_as("SELECT 1")
        .fetch_one(&pool)
        .await?;
    
    if row.0 != 1 {
        anyhow::bail!("Échec de la vérification de connexion à la base de données");
    }

    // Configuration de sécurité PostgreSQL
    security::configure_database_security(&pool).await?;

    log::info!("Base de données initialisée avec succès");
    Ok(pool)
}

/// Vérifie l'état de santé de la base de données
pub async fn health_check(pool: &DatabasePool) -> Result<bool> {
    let result = sqlx::query("SELECT 1")
        .fetch_one(pool)
        .await;

    match result {
        Ok(_) => Ok(true),
        Err(e) => {
            log::error!("Échec du health check de la base de données: {}", e);
            Ok(false)
        }
    }
}
