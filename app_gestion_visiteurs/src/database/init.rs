//! Initialisation de la base de données avec données par défaut
//! Création de l'utilisateur administrateur et configuration initiale

use anyhow::Result;
use sqlx::PgPool;
use tracing::{info, warn};

use crate::database::users::{UserRepository, CreateUserRequest};

/// Initialiser la base de données avec les données par défaut
pub async fn initialize_default_data(pool: &PgPool) -> Result<()> {
    info!("🔧 Initialisation des données par défaut...");
    
    // Créer l'utilisateur administrateur par défaut
    create_default_admin(pool).await?;
    
    info!("✅ Données par défaut initialisées avec succès");
    Ok(())
}

/// Créer l'utilisateur administrateur par défaut
async fn create_default_admin(pool: &PgPool) -> Result<()> {
    let user_repo = UserRepository::new(pool.clone());
    
    // Vérifier si l'admin existe déjà
    if let Ok(Some(_)) = user_repo.get_user_by_username("admin").await {
        info!("👤 Utilisateur admin existe déjà");
        return Ok(());
    }
    
    // Créer l'utilisateur admin
    let admin_request = CreateUserRequest {
        username: "admin".to_string(),
        email: "<EMAIL>".to_string(),
        password: "Admin123!".to_string(), // Mot de passe par défaut - À CHANGER EN PRODUCTION
        role: "admin".to_string(),
    };
    
    match user_repo.create_user(admin_request, None).await {
        Ok(user) => {
            info!("✅ Utilisateur administrateur créé: {} ({})", user.username, user.id);
            warn!("⚠️  SÉCURITÉ: Changez le mot de passe par défaut de l'admin en production !");
        }
        Err(e) => {
            warn!("⚠️  Impossible de créer l'utilisateur admin: {}", e);
        }
    }
    
    Ok(())
}

/// Créer des utilisateurs de test pour le développement
pub async fn create_test_users(pool: &PgPool) -> Result<()> {
    if std::env::var("APP_ENV").unwrap_or_default() != "development" {
        return Ok(());
    }
    
    info!("🔧 Création des utilisateurs de test...");
    let user_repo = UserRepository::new(pool.clone());
    
    // Utilisateur opérateur
    if user_repo.get_user_by_username("operator").await.is_err() {
        let operator_request = CreateUserRequest {
            username: "operator".to_string(),
            email: "<EMAIL>".to_string(),
            password: "Operator123!".to_string(),
            role: "operator".to_string(),
        };
        
        if let Ok(user) = user_repo.create_user(operator_request, None).await {
            info!("✅ Utilisateur opérateur créé: {}", user.username);
        }
    }
    
    // Utilisateur standard
    if user_repo.get_user_by_username("user").await.is_err() {
        let user_request = CreateUserRequest {
            username: "user".to_string(),
            email: "<EMAIL>".to_string(),
            password: "User123!".to_string(),
            role: "user".to_string(),
        };
        
        if let Ok(user) = user_repo.create_user(user_request, None).await {
            info!("✅ Utilisateur standard créé: {}", user.username);
        }
    }
    
    Ok(())
}
