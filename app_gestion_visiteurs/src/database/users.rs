//! Module de gestion des utilisateurs avec sécurité renforcée
//! Implémentation avec hachage Argon2, audit et protection contre les attaques

use anyhow::{Result, anyhow};
use argon2::{Argon2, PasswordHash, PasswordHasher, PasswordVerifier};
use argon2::password_hash::{rand_core::OsRng, SaltString};
use chrono::{DateTime, Utc, Duration};
use serde::{Deserialize, Serialize};
use sqlx::{PgPool, FromRow};
use uuid::Uuid;
use tracing::{info, warn, error};

/// Modèle utilisateur sécurisé pour l'authentification
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct SecureUser {
    pub id: Uuid,
    pub username: String,
    pub email: String,
    #[serde(skip_serializing)]
    pub password_hash: String,
    pub role: String,
    pub is_active: bool,
    pub last_login: Option<DateTime<Utc>>,
    pub failed_login_attempts: i32,
    pub account_locked_until: Option<DateTime<Utc>>,
    pub password_changed_at: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub created_by: Option<Uuid>,
    pub updated_by: Option<Uuid>,
}

/// Données pour créer un nouvel utilisateur
#[derive(Debug, Deserialize)]
pub struct CreateUserRequest {
    pub username: String,
    pub email: String,
    pub password: String,
    pub role: String,
}

/// Données pour l'authentification
#[derive(Debug, Deserialize)]
pub struct LoginCredentials {
    pub username: String,
    pub password: String,
}

/// Résultat de l'authentification
#[derive(Debug, Serialize)]
pub struct AuthResult {
    pub success: bool,
    pub user: Option<SecureUser>,
    pub token: Option<String>,
    pub message: String,
    pub locked_until: Option<DateTime<Utc>>,
}

/// Repository pour la gestion des utilisateurs
pub struct UserRepository {
    pool: PgPool,
}

impl UserRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// Créer un nouvel utilisateur avec hachage sécurisé
    pub async fn create_user(&self, request: CreateUserRequest, created_by: Option<Uuid>) -> Result<SecureUser> {
        // Vérifier si l'utilisateur existe déjà
        if self.user_exists(&request.username, &request.email).await? {
            return Err(anyhow!("Utilisateur ou email déjà existant"));
        }

        // Hacher le mot de passe avec Argon2
        let password_hash = self.hash_password(&request.password)?;
        let user_id = Uuid::new_v4();
        let now = Utc::now();

        let user = sqlx::query_as::<_, SecureUser>(
            r#"
            INSERT INTO secure_users (
                id, username, email, password_hash, role, is_active,
                failed_login_attempts, password_changed_at, created_at, updated_at, created_by
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            RETURNING *
            "#
        )
        .bind(user_id)
        .bind(&request.username)
        .bind(&request.email)
        .bind(&password_hash)
        .bind(&request.role)
        .bind(true)
        .bind(0)
        .bind(now)
        .bind(now)
        .bind(now)
        .bind(created_by)
        .fetch_one(&self.pool)
        .await?;

        info!("Nouvel utilisateur créé: {} ({})", user.username, user.id);
        Ok(user)
    }

    /// Authentifier un utilisateur
    pub async fn authenticate_user(&self, credentials: LoginCredentials, ip_address: &str) -> Result<AuthResult> {
        // Récupérer l'utilisateur
        let user_result = sqlx::query_as::<_, SecureUser>(
            "SELECT * FROM secure_users WHERE username = $1 AND is_active = true"
        )
        .bind(&credentials.username)
        .fetch_optional(&self.pool)
        .await?;

        let mut user = match user_result {
            Some(user) => user,
            None => {
                warn!("Tentative de connexion avec utilisateur inexistant: {} depuis {}", credentials.username, ip_address);
                return Ok(AuthResult {
                    success: false,
                    user: None,
                    token: None,
                    message: "Identifiants invalides".to_string(),
                    locked_until: None,
                });
            }
        };

        // Vérifier si le compte est verrouillé
        if let Some(locked_until) = user.account_locked_until {
            if locked_until > Utc::now() {
                warn!("Tentative de connexion sur compte verrouillé: {} depuis {}", credentials.username, ip_address);
                return Ok(AuthResult {
                    success: false,
                    user: None,
                    token: None,
                    message: "Compte temporairement verrouillé".to_string(),
                    locked_until: Some(locked_until),
                });
            }
        }

        // Vérifier le mot de passe
        let password_valid = self.verify_password(&credentials.password, &user.password_hash)?;

        if password_valid {
            // Connexion réussie
            self.handle_successful_login(&mut user, ip_address).await?;
            
            // Créer le token JWT
            let token = crate::utils::advanced_jwt::create_advanced_jwt(
                &user.username,
                &user.role,
                vec!["read".to_string(), "write".to_string()], // Permissions par défaut
                ip_address,
                "DCOP413-App"
            )?;

            info!("Connexion réussie: {} depuis {}", user.username, ip_address);
            Ok(AuthResult {
                success: true,
                user: Some(user),
                token: Some(token),
                message: "Connexion réussie".to_string(),
                locked_until: None,
            })
        } else {
            // Connexion échouée
            self.handle_failed_login(&mut user, ip_address).await?;
            
            warn!("Échec de connexion: {} depuis {} (tentative {})", 
                  user.username, ip_address, user.failed_login_attempts);
            
            Ok(AuthResult {
                success: false,
                user: None,
                token: None,
                message: "Identifiants invalides".to_string(),
                locked_until: user.account_locked_until,
            })
        }
    }

    /// Vérifier si un utilisateur existe
    async fn user_exists(&self, username: &str, email: &str) -> Result<bool> {
        let count: (i64,) = sqlx::query_as(
            "SELECT COUNT(*) FROM secure_users WHERE username = $1 OR email = $2"
        )
        .bind(username)
        .bind(email)
        .fetch_one(&self.pool)
        .await?;

        Ok(count.0 > 0)
    }

    /// Hacher un mot de passe avec Argon2
    fn hash_password(&self, password: &str) -> Result<String> {
        let salt = SaltString::generate(&mut OsRng);
        let argon2 = Argon2::default();
        
        let password_hash = argon2
            .hash_password(password.as_bytes(), &salt)
            .map_err(|e| anyhow!("Erreur de hachage: {}", e))?
            .to_string();

        Ok(password_hash)
    }

    /// Vérifier un mot de passe
    fn verify_password(&self, password: &str, hash: &str) -> Result<bool> {
        let parsed_hash = PasswordHash::new(hash)
            .map_err(|e| anyhow!("Hash invalide: {}", e))?;
        
        let argon2 = Argon2::default();
        Ok(argon2.verify_password(password.as_bytes(), &parsed_hash).is_ok())
    }

    /// Gérer une connexion réussie
    async fn handle_successful_login(&self, user: &mut SecureUser, ip_address: &str) -> Result<()> {
        let now = Utc::now();
        user.last_login = Some(now);
        user.failed_login_attempts = 0;
        user.account_locked_until = None;
        user.updated_at = now;

        sqlx::query(
            r#"
            UPDATE secure_users 
            SET last_login = $1, failed_login_attempts = 0, 
                account_locked_until = NULL, updated_at = $2
            WHERE id = $3
            "#
        )
        .bind(now)
        .bind(now)
        .bind(user.id)
        .execute(&self.pool)
        .await?;

        // Enregistrer l'audit de connexion
        self.log_login_attempt(user.id, ip_address, true).await?;
        Ok(())
    }

    /// Gérer une connexion échouée
    async fn handle_failed_login(&self, user: &mut SecureUser, ip_address: &str) -> Result<()> {
        let now = Utc::now();
        user.failed_login_attempts += 1;
        user.updated_at = now;

        // Verrouiller le compte après 5 tentatives échouées
        if user.failed_login_attempts >= 5 {
            user.account_locked_until = Some(now + Duration::minutes(30));
            warn!("Compte verrouillé pour 30 minutes: {} après {} tentatives", 
                  user.username, user.failed_login_attempts);
        }

        sqlx::query(
            r#"
            UPDATE secure_users 
            SET failed_login_attempts = $1, account_locked_until = $2, updated_at = $3
            WHERE id = $4
            "#
        )
        .bind(user.failed_login_attempts)
        .bind(user.account_locked_until)
        .bind(now)
        .bind(user.id)
        .execute(&self.pool)
        .await?;

        // Enregistrer l'audit de connexion échouée
        self.log_login_attempt(user.id, ip_address, false).await?;
        Ok(())
    }

    /// Enregistrer une tentative de connexion pour audit
    async fn log_login_attempt(&self, user_id: Uuid, ip_address: &str, success: bool) -> Result<()> {
        sqlx::query(
            r#"
            INSERT INTO login_audit (id, user_id, ip_address, success, attempted_at)
            VALUES ($1, $2, $3, $4, $5)
            "#
        )
        .bind(Uuid::new_v4())
        .bind(user_id)
        .bind(ip_address)
        .bind(success)
        .bind(Utc::now())
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// Récupérer un utilisateur par ID
    pub async fn get_user_by_id(&self, user_id: Uuid) -> Result<Option<SecureUser>> {
        let user = sqlx::query_as::<_, SecureUser>(
            "SELECT * FROM secure_users WHERE id = $1 AND is_active = true"
        )
        .bind(user_id)
        .fetch_optional(&self.pool)
        .await?;

        Ok(user)
    }

    /// Récupérer un utilisateur par nom d'utilisateur
    pub async fn get_user_by_username(&self, username: &str) -> Result<Option<SecureUser>> {
        let user = sqlx::query_as::<_, SecureUser>(
            "SELECT * FROM secure_users WHERE username = $1 AND is_active = true"
        )
        .bind(username)
        .fetch_optional(&self.pool)
        .await?;

        Ok(user)
    }
}
