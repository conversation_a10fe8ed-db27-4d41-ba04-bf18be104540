// Métriques avec l'API moderne
use metrics::{counter, gauge, histogram, describe_counter, describe_gauge, describe_histogram};
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use tracing::{info, warn, error};

/// Métriques de l'application
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ApplicationMetrics {
    pub requests_total: u64,
    pub requests_per_minute: u64,
    pub active_sessions: u64,
    pub failed_logins: u64,
    pub successful_logins: u64,
    pub visitors_created: u64,
    pub visits_created: u64,
    pub security_events: u64,
    pub response_time_avg: f64,
    pub error_rate: f64,
    pub uptime_seconds: u64,
}

/// Gestionnaire de métriques avancé
pub struct MetricsManager {
    start_time: Instant,
    request_times: Arc<Mutex<Vec<Duration>>>,
    error_count: Arc<Mutex<u64>>,
    request_count: Arc<Mutex<u64>>,
    custom_metrics: Arc<Mutex<HashMap<String, f64>>>,
}

impl MetricsManager {
    pub fn new() -> Self {
        // Décrire les métriques (API moderne)
        describe_counter!("dcop413_requests_total", "Total number of HTTP requests");
        describe_counter!("dcop413_login_attempts_total", "Total number of login attempts");
        describe_counter!("dcop413_security_events_total", "Total number of security events");
        describe_counter!("dcop413_visitors_created_total", "Total number of visitors created");
        describe_counter!("dcop413_visits_created_total", "Total number of visits created");

        describe_gauge!("dcop413_active_sessions", "Number of active user sessions");
        describe_gauge!("dcop413_response_time_avg", "Average response time in milliseconds");
        describe_gauge!("dcop413_error_rate", "Current error rate percentage");
        describe_gauge!("dcop413_uptime_seconds", "Application uptime in seconds");

        describe_histogram!("dcop413_request_duration", "HTTP request duration in seconds");
        
        Self {
            start_time: Instant::now(),
            request_times: Arc::new(Mutex::new(Vec::new())),
            error_count: Arc::new(Mutex::new(0)),
            request_count: Arc::new(Mutex::new(0)),
            custom_metrics: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    /// Enregistrer une requête HTTP
    pub fn record_request(&self, duration: Duration, is_error: bool) {
        // Incrémenter le compteur de requêtes
        counter!("dcop413_requests_total").increment(1);
        
        // Enregistrer la durée
        histogram!("dcop413_request_duration").record(duration.as_secs_f64());
        
        // Mettre à jour les statistiques internes
        if let Ok(mut times) = self.request_times.lock() {
            times.push(duration);
            // Garder seulement les 1000 dernières requêtes
            if times.len() > 1000 {
                times.remove(0);
            }
        }
        
        if let Ok(mut count) = self.request_count.lock() {
            *count += 1;
        }
        
        if is_error {
            if let Ok(mut errors) = self.error_count.lock() {
                *errors += 1;
            }
        }
        
        // Mettre à jour les métriques calculées
        self.update_calculated_metrics();
    }

    /// Enregistrer une tentative de connexion
    pub fn record_login_attempt(&self, success: bool, username: &str, ip: &str) {
        counter!("dcop413_login_attempts_total").increment(1);
        
        if success {
            info!("Connexion réussie pour l'utilisateur: {} depuis IP: {}", username, ip);
            self.increment_custom_metric("successful_logins");
        } else {
            warn!("Échec de connexion pour l'utilisateur: {} depuis IP: {}", username, ip);
            self.increment_custom_metric("failed_logins");
        }
    }

    /// Enregistrer un événement de sécurité
    pub fn record_security_event(&self, event_type: &str, severity: &str, ip: &str) {
        counter!("dcop413_security_events_total").increment(1);
        
        error!("Événement de sécurité: {} ({}), IP: {}", event_type, severity, ip);
        
        self.increment_custom_metric("security_events");
        
        // Métriques spécifiques par type d'événement
        let metric_name = format!("security_event_{}", event_type.to_lowercase());
        self.increment_custom_metric(&metric_name);
    }

    /// Enregistrer la création d'un visiteur
    pub fn record_visitor_created(&self, visitor_name: &str) {
        counter!("dcop413_visitors_created_total").increment(1);
        info!("Nouveau visiteur créé: {}", visitor_name);
        self.increment_custom_metric("visitors_created");
    }

    /// Enregistrer la création d'une visite
    pub fn record_visit_created(&self, visit_id: &str, visitor_name: &str) {
        counter!("dcop413_visits_created_total").increment(1);
        info!("Nouvelle visite créée: {} pour {}", visit_id, visitor_name);
        self.increment_custom_metric("visits_created");
    }

    /// Mettre à jour le nombre de sessions actives
    pub fn update_active_sessions(&self, count: u64) {
        gauge!("dcop413_active_sessions").set(count as f64);
        self.set_custom_metric("active_sessions", count as f64);
    }

    /// Incrémenter une métrique personnalisée
    fn increment_custom_metric(&self, name: &str) {
        if let Ok(mut metrics) = self.custom_metrics.lock() {
            let current = *metrics.get(name).unwrap_or(&0.0);
            metrics.insert(name.to_string(), current + 1.0);
        }
    }

    /// Définir une métrique personnalisée
    fn set_custom_metric(&self, name: &str, value: f64) {
        if let Ok(mut metrics) = self.custom_metrics.lock() {
            metrics.insert(name.to_string(), value);
        }
    }

    /// Mettre à jour les métriques calculées
    fn update_calculated_metrics(&self) {
        // Calculer le temps de réponse moyen
        if let Ok(times) = self.request_times.lock() {
            if !times.is_empty() {
                let avg_ms = times.iter()
                    .map(|d| d.as_millis() as f64)
                    .sum::<f64>() / times.len() as f64;
                
                gauge!("dcop413_response_time_avg").set(avg_ms);
                self.set_custom_metric("response_time_avg", avg_ms);
            }
        }
        
        // Calculer le taux d'erreur
        if let (Ok(errors), Ok(requests)) = (self.error_count.lock(), self.request_count.lock()) {
            if *requests > 0 {
                let error_rate = (*errors as f64 / *requests as f64) * 100.0;
                gauge!("dcop413_error_rate").set(error_rate);
                self.set_custom_metric("error_rate", error_rate);
            }
        }
        
        // Mettre à jour l'uptime
        let uptime = self.start_time.elapsed().as_secs();
        gauge!("dcop413_uptime_seconds").set(uptime as f64);
        self.set_custom_metric("uptime_seconds", uptime as f64);
    }

    /// Obtenir toutes les métriques actuelles
    pub fn get_metrics(&self) -> ApplicationMetrics {
        self.update_calculated_metrics();
        
        let custom_metrics = self.custom_metrics.lock().unwrap_or_else(|_| {
            std::sync::PoisonError::into_inner(self.custom_metrics.lock().unwrap_err())
        });
        
        ApplicationMetrics {
            requests_total: custom_metrics.get("requests_total").unwrap_or(&0.0).clone() as u64,
            requests_per_minute: self.calculate_requests_per_minute(),
            active_sessions: custom_metrics.get("active_sessions").unwrap_or(&0.0).clone() as u64,
            failed_logins: custom_metrics.get("failed_logins").unwrap_or(&0.0).clone() as u64,
            successful_logins: custom_metrics.get("successful_logins").unwrap_or(&0.0).clone() as u64,
            visitors_created: custom_metrics.get("visitors_created").unwrap_or(&0.0).clone() as u64,
            visits_created: custom_metrics.get("visits_created").unwrap_or(&0.0).clone() as u64,
            security_events: custom_metrics.get("security_events").unwrap_or(&0.0).clone() as u64,
            response_time_avg: custom_metrics.get("response_time_avg").unwrap_or(&0.0).clone(),
            error_rate: custom_metrics.get("error_rate").unwrap_or(&0.0).clone(),
            uptime_seconds: self.start_time.elapsed().as_secs(),
        }
    }

    /// Calculer les requêtes par minute
    fn calculate_requests_per_minute(&self) -> u64 {
        if let Ok(times) = self.request_times.lock() {
            let one_minute_ago = Instant::now() - Duration::from_secs(60);
            let recent_requests = times.iter()
                .filter(|&duration| {
                    // Approximation: considérer les requêtes récentes
                    duration.as_secs() < 60
                })
                .count();
            recent_requests as u64
        } else {
            0
        }
    }

    /// Obtenir les métriques de santé du système
    pub fn get_health_metrics(&self) -> HashMap<String, serde_json::Value> {
        let mut health = HashMap::new();
        
        let metrics = self.get_metrics();
        
        // Statut général
        let is_healthy = metrics.error_rate < 5.0 && metrics.response_time_avg < 1000.0;
        health.insert("status".to_string(), serde_json::Value::String(
            if is_healthy { "healthy".to_string() } else { "degraded".to_string() }
        ));
        
        // Métriques clés
        health.insert("uptime_seconds".to_string(), serde_json::Value::Number(metrics.uptime_seconds.into()));
        health.insert("requests_total".to_string(), serde_json::Value::Number(metrics.requests_total.into()));
        health.insert("error_rate".to_string(), serde_json::Value::Number(
            serde_json::Number::from_f64(metrics.error_rate).unwrap_or(serde_json::Number::from(0))
        ));
        health.insert("response_time_avg".to_string(), serde_json::Value::Number(
            serde_json::Number::from_f64(metrics.response_time_avg).unwrap_or(serde_json::Number::from(0))
        ));
        health.insert("active_sessions".to_string(), serde_json::Value::Number(metrics.active_sessions.into()));
        
        // Timestamp
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        health.insert("timestamp".to_string(), serde_json::Value::Number(timestamp.into()));
        
        health
    }

    /// Réinitialiser les métriques (pour les tests)
    pub fn reset_metrics(&self) {
        if let Ok(mut metrics) = self.custom_metrics.lock() {
            metrics.clear();
        }
        
        if let Ok(mut times) = self.request_times.lock() {
            times.clear();
        }
        
        if let Ok(mut errors) = self.error_count.lock() {
            *errors = 0;
        }
        
        if let Ok(mut requests) = self.request_count.lock() {
            *requests = 0;
        }
    }
}

// Instance globale du gestionnaire de métriques
use std::sync::OnceLock;
static METRICS_MANAGER: OnceLock<MetricsManager> = OnceLock::new();

pub fn get_metrics_manager() -> &'static MetricsManager {
    METRICS_MANAGER.get_or_init(|| MetricsManager::new())
}

/// Enregistrer une requête HTTP
pub fn record_request(duration: Duration, is_error: bool) {
    get_metrics_manager().record_request(duration, is_error);
}

/// Enregistrer une tentative de connexion
pub fn record_login_attempt(success: bool, username: &str, ip: &str) {
    get_metrics_manager().record_login_attempt(success, username, ip);
}

/// Enregistrer un événement de sécurité
pub fn record_security_event(event_type: &str, severity: &str, ip: &str) {
    get_metrics_manager().record_security_event(event_type, severity, ip);
}

/// Enregistrer la création d'un visiteur
pub fn record_visitor_created(visitor_name: &str) {
    get_metrics_manager().record_visitor_created(visitor_name);
}

/// Enregistrer la création d'une visite
pub fn record_visit_created(visit_id: &str, visitor_name: &str) {
    get_metrics_manager().record_visit_created(visit_id, visitor_name);
}

/// Mettre à jour le nombre de sessions actives
pub fn update_active_sessions(count: u64) {
    get_metrics_manager().update_active_sessions(count);
}

/// Obtenir toutes les métriques
pub fn get_metrics() -> ApplicationMetrics {
    get_metrics_manager().get_metrics()
}

/// Obtenir les métriques de santé
pub fn get_health_metrics() -> HashMap<String, serde_json::Value> {
    get_metrics_manager().get_health_metrics()
}
