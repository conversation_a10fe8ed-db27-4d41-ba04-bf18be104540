use actix_web::{
    dev::{forward_ready, Service, ServiceRequest, ServiceResponse, Transform},
    Error, HttpMessage, HttpResponse, Result,
};
use futures_util::future::LocalBoxFuture;
use std::{
    collections::HashMap,
    future::{ready, Ready},
    rc::Rc,
    sync::{Arc, Mutex},
    time::{Duration, Instant},
};
use governor::{Quota, RateLimiter, state::{InMemoryState, NotKeyed}};
use tracing::{warn, error, info};
use crate::utils::security::{log_security_event, get_client_ip, get_user_agent, is_suspicious_user_agent};

/// Configuration de sécurité avancée
#[derive(Debug, Clone)]
pub struct AdvancedSecurityConfig {
    pub rate_limit_requests_per_minute: u32,
    pub rate_limit_burst: u32,
    pub enable_csrf_protection: bool,
    pub enable_xss_protection: bool,
    pub enable_sql_injection_detection: bool,
    pub enable_suspicious_ua_blocking: bool,
    pub max_request_size: usize,
    pub blocked_ips: Vec<String>,
    pub allowed_origins: Vec<String>,
}

impl Default for AdvancedSecurityConfig {
    fn default() -> Self {
        Self {
            rate_limit_requests_per_minute: 100,
            rate_limit_burst: 10,
            enable_csrf_protection: true,
            enable_xss_protection: true,
            enable_sql_injection_detection: true,
            enable_suspicious_ua_blocking: true,
            max_request_size: 10 * 1024 * 1024, // 10MB
            blocked_ips: Vec::new(),
            allowed_origins: vec![
                "http://localhost:8080".to_string(),
                "https://dcop413.local".to_string(),
            ],
        }
    }
}

/// Middleware de sécurité avancé
pub struct AdvancedSecurityMiddleware {
    config: AdvancedSecurityConfig,
    rate_limiter: Arc<RateLimiter<NotKeyed, InMemoryState, governor::clock::DefaultClock>>,
    blocked_ips_cache: Arc<Mutex<HashMap<String, Instant>>>,
    suspicious_activities: Arc<Mutex<HashMap<String, u32>>>,
}

impl AdvancedSecurityMiddleware {
    pub fn new(config: AdvancedSecurityConfig) -> Self {
        let quota = Quota::per_minute(std::num::NonZeroU32::new(config.rate_limit_requests_per_minute).unwrap())
            .allow_burst(std::num::NonZeroU32::new(config.rate_limit_burst).unwrap());
        
        let rate_limiter = Arc::new(RateLimiter::direct(quota));
        
        Self {
            config,
            rate_limiter,
            blocked_ips_cache: Arc::new(Mutex::new(HashMap::new())),
            suspicious_activities: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    /// Vérifier le rate limiting
    fn check_rate_limit(&self, ip: &str) -> bool {
        match self.rate_limiter.check() {
            Ok(_) => true,
            Err(_) => {
                warn!("Rate limit dépassé pour IP: {}", ip);
                
                // Incrémenter les activités suspectes
                if let Ok(mut activities) = self.suspicious_activities.lock() {
                    let count = activities.entry(ip.to_string()).or_insert(0);
                    *count += 1;
                    
                    // Bloquer temporairement après 5 violations
                    if *count >= 5 {
                        if let Ok(mut blocked) = self.blocked_ips_cache.lock() {
                            blocked.insert(ip.to_string(), Instant::now());
                        }
                        error!("IP bloquée temporairement pour violations répétées: {}", ip);
                    }
                }
                
                false
            }
        }
    }

    /// Vérifier si une IP est bloquée
    fn is_ip_blocked(&self, ip: &str) -> bool {
        // Vérifier la liste statique
        if self.config.blocked_ips.contains(&ip.to_string()) {
            return true;
        }
        
        // Vérifier le cache temporaire
        if let Ok(mut blocked) = self.blocked_ips_cache.lock() {
            if let Some(blocked_time) = blocked.get(ip) {
                // Débloquer après 1 heure
                if blocked_time.elapsed() > Duration::from_secs(3600) {
                    blocked.remove(ip);
                    info!("IP débloquée après expiration: {}", ip);
                    false
                } else {
                    true
                }
            } else {
                false
            }
        } else {
            false
        }
    }

    /// Détecter les patterns d'injection SQL
    fn detect_sql_injection(&self, content: &str) -> bool {
        let sql_patterns = [
            r"(?i)\bunion\b.*\bselect\b",
            r"(?i)\bdrop\b.*\btable\b",
            r"(?i)\bdelete\b.*\bfrom\b",
            r"(?i)\binsert\b.*\binto\b",
            r"(?i)\bupdate\b.*\bset\b",
            r"--|\#|/\*|\*/",
            r"'.*'.*=.*'.*'",
            r"\bor\b.*\b1\b.*=.*\b1\b",
        ];
        
        for pattern in &sql_patterns {
            if let Ok(regex) = regex::Regex::new(pattern) {
                if regex.is_match(content) {
                    return true;
                }
            }
        }
        
        false
    }

    /// Détecter les patterns XSS
    fn detect_xss(&self, content: &str) -> bool {
        let xss_patterns = [
            r"<\s*script[^>]*>",
            r"javascript\s*:",
            r"on\w+\s*=",
            r"<\s*iframe[^>]*>",
            r"<\s*object[^>]*>",
            r"<\s*embed[^>]*>",
            r"eval\s*\(",
            r"document\s*\.\s*cookie",
        ];
        
        for pattern in &xss_patterns {
            if let Ok(regex) = regex::Regex::new(pattern) {
                if regex.is_match(content) {
                    return true;
                }
            }
        }
        
        false
    }

    /// Analyser la requête pour détecter les menaces
    fn analyze_request(&self, req: &ServiceRequest) -> Result<(), String> {
        let ip = get_client_ip(req.request());
        let user_agent = get_user_agent(req.request());
        
        // Vérifier IP bloquée
        if self.is_ip_blocked(&ip) {
            return Err("IP bloquée".to_string());
        }
        
        // Vérifier rate limiting
        if !self.check_rate_limit(&ip) {
            return Err("Rate limit dépassé".to_string());
        }
        
        // Vérifier User-Agent suspect
        if self.config.enable_suspicious_ua_blocking && is_suspicious_user_agent(&user_agent) {
            warn!("User-Agent suspect détecté: {} depuis IP: {}", user_agent, ip);
            
            let mut details = HashMap::new();
            details.insert("user_agent".to_string(), user_agent.clone());
            details.insert("reason".to_string(), "Suspicious User-Agent".to_string());
            
            log_security_event("SUSPICIOUS_USER_AGENT", "MEDIUM", req.request(), details);
        }
        
        // Analyser l'URL pour les injections
        let path = req.path();
        let query = req.query_string();
        
        if self.config.enable_sql_injection_detection {
            if self.detect_sql_injection(path) || self.detect_sql_injection(query) {
                error!("Tentative d'injection SQL détectée depuis IP: {}", ip);
                
                let mut details = HashMap::new();
                details.insert("path".to_string(), path.to_string());
                details.insert("query".to_string(), query.to_string());
                details.insert("attack_type".to_string(), "SQL Injection".to_string());
                
                log_security_event("SQL_INJECTION_ATTEMPT", "HIGH", req.request(), details);
                
                return Err("Tentative d'injection SQL détectée".to_string());
            }
        }
        
        if self.config.enable_xss_protection {
            if self.detect_xss(path) || self.detect_xss(query) {
                error!("Tentative XSS détectée depuis IP: {}", ip);
                
                let mut details = HashMap::new();
                details.insert("path".to_string(), path.to_string());
                details.insert("query".to_string(), query.to_string());
                details.insert("attack_type".to_string(), "XSS".to_string());
                
                log_security_event("XSS_ATTEMPT", "HIGH", req.request(), details);
                
                return Err("Tentative XSS détectée".to_string());
            }
        }
        
        Ok(())
    }

    /// Ajouter les headers de sécurité OWASP
    fn add_security_headers(&self, mut res: ServiceResponse) -> ServiceResponse {
        let headers = res.headers_mut();
        
        // Headers de sécurité OWASP
        headers.insert(
            actix_web::http::header::HeaderName::from_static("x-content-type-options"),
            actix_web::http::HeaderValue::from_static("nosniff"),
        );
        
        headers.insert(
            actix_web::http::header::HeaderName::from_static("x-frame-options"),
            actix_web::http::HeaderValue::from_static("DENY"),
        );
        
        headers.insert(
            actix_web::http::header::HeaderName::from_static("x-xss-protection"),
            actix_web::http::HeaderValue::from_static("1; mode=block"),
        );
        
        headers.insert(
            actix_web::http::header::HeaderName::from_static("strict-transport-security"),
            actix_web::http::HeaderValue::from_static("max-age=31536000; includeSubDomains"),
        );
        
        headers.insert(
            actix_web::http::header::HeaderName::from_static("content-security-policy"),
            actix_web::http::HeaderValue::from_static(
                "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com; style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com; img-src 'self' data:; font-src 'self'"
            ),
        );
        
        headers.insert(
            actix_web::http::header::HeaderName::from_static("referrer-policy"),
            actix_web::http::HeaderValue::from_static("strict-origin-when-cross-origin"),
        );
        
        headers.insert(
            actix_web::http::header::HeaderName::from_static("permissions-policy"),
            actix_web::http::HeaderValue::from_static("geolocation=(), microphone=(), camera=()"),
        );
        
        headers.insert(
            actix_web::http::header::HeaderName::from_static("x-permitted-cross-domain-policies"),
            actix_web::http::HeaderValue::from_static("none"),
        );
        
        headers.insert(
            actix_web::http::header::HeaderName::from_static("cross-origin-embedder-policy"),
            actix_web::http::HeaderValue::from_static("require-corp"),
        );
        
        headers.insert(
            actix_web::http::header::HeaderName::from_static("cross-origin-opener-policy"),
            actix_web::http::HeaderValue::from_static("same-origin"),
        );
        
        headers.insert(
            actix_web::http::header::HeaderName::from_static("cross-origin-resource-policy"),
            actix_web::http::HeaderValue::from_static("same-origin"),
        );
        
        headers.insert(
            actix_web::http::header::HeaderName::from_static("cache-control"),
            actix_web::http::HeaderValue::from_static("no-store, no-cache, must-revalidate, proxy-revalidate"),
        );
        
        res
    }
}

impl<S, B> Transform<S, ServiceRequest> for AdvancedSecurityMiddleware
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error>,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type InitError = ();
    type Transform = AdvancedSecurityService<S>;
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(AdvancedSecurityService {
            service,
            middleware: self.clone(),
        }))
    }
}

pub struct AdvancedSecurityService<S> {
    service: S,
    middleware: AdvancedSecurityMiddleware,
}

impl<S, B> Service<ServiceRequest> for AdvancedSecurityService<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error>,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = LocalBoxFuture<'static, Result<Self::Response, Self::Error>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let middleware = self.middleware.clone();
        
        // Analyser la requête
        if let Err(error_msg) = middleware.analyze_request(&req) {
            let ip = get_client_ip(req.request());
            error!("Requête bloquée depuis IP {}: {}", ip, error_msg);
            
            let response = HttpResponse::Forbidden()
                .json(serde_json::json!({
                    "error": "Accès refusé",
                    "message": "Votre requête a été bloquée pour des raisons de sécurité"
                }));
            
            return Box::pin(async move {
                Ok(req.into_response(response))
            });
        }
        
        let fut = self.service.call(req);
        
        Box::pin(async move {
            let res = fut.await?;
            Ok(middleware.add_security_headers(res))
        })
    }
}

impl Clone for AdvancedSecurityMiddleware {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            rate_limiter: Arc::clone(&self.rate_limiter),
            blocked_ips_cache: Arc::clone(&self.blocked_ips_cache),
            suspicious_activities: Arc::clone(&self.suspicious_activities),
        }
    }
}
