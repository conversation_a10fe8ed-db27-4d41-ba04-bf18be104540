//! Middleware d'authentification sécurisé
//! Protection automatique des routes avec JWT et sessions

use actix_web::{
    dev::{forward_ready, Service, ServiceRequest, ServiceResponse, Transform},
    Error, HttpMessage, HttpResponse,
};
use actix_session::SessionExt;
use futures_util::future::LocalBoxFuture;
use serde_json::json;
use std::{
    future::{ready, Ready},
    rc::Rc,
};
use tracing::{warn, error, debug};

use crate::utils::advanced_jwt::{validate_advanced_jwt, AdvancedClaims};

/// Middleware d'authentification
pub struct AuthMiddleware {
    /// Routes qui nécessitent une authentification
    protected_paths: Vec<String>,
    /// Routes qui sont publiques (pas d'authentification requise)
    public_paths: Vec<String>,
}

impl AuthMiddleware {
    pub fn new() -> Self {
        Self {
            protected_paths: vec![
                "/accueil".to_string(),
                "/formulaire".to_string(),
                "/main".to_string(),
                "/photo".to_string(),
                "/api/".to_string(),
                "/admin/".to_string(),
            ],
            public_paths: vec![
                "/".to_string(),
                "/login".to_string(),
                "/auth/login".to_string(),
                "/auth/verify".to_string(),
                "/static/".to_string(),
                "/css/".to_string(),
                "/js/".to_string(),
                "/js_ts/".to_string(),
                "/images/".to_string(),
                "/favicon.ico".to_string(),
            ],
        }
    }

    /// Vérifier si un chemin nécessite une authentification
    fn requires_auth(&self, path: &str) -> bool {
        // Vérifier d'abord si c'est une route publique
        for public_path in &self.public_paths {
            if path.starts_with(public_path) {
                return false;
            }
        }

        // Vérifier si c'est une route protégée
        for protected_path in &self.protected_paths {
            if path.starts_with(protected_path) {
                return true;
            }
        }

        // Par défaut, les routes non spécifiées nécessitent une authentification
        true
    }
}

impl Default for AuthMiddleware {
    fn default() -> Self {
        Self::new()
    }
}

impl<S, B> Transform<S, ServiceRequest> for AuthMiddleware
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type InitError = ();
    type Transform = AuthMiddlewareService<S>;
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(AuthMiddlewareService {
            service: Rc::new(service),
            protected_paths: self.protected_paths.clone(),
            public_paths: self.public_paths.clone(),
        }))
    }
}

pub struct AuthMiddlewareService<S> {
    service: Rc<S>,
    protected_paths: Vec<String>,
    public_paths: Vec<String>,
}

impl<S> AuthMiddlewareService<S> {
    fn requires_auth(&self, path: &str) -> bool {
        // Vérifier d'abord si c'est une route publique
        for public_path in &self.public_paths {
            if path.starts_with(public_path) {
                return false;
            }
        }

        // Vérifier si c'est une route protégée
        for protected_path in &self.protected_paths {
            if path.starts_with(protected_path) {
                return true;
            }
        }

        // Par défaut, les routes non spécifiées nécessitent une authentification
        true
    }
}

impl<S, B> Service<ServiceRequest> for AuthMiddlewareService<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = LocalBoxFuture<'static, Result<Self::Response, Self::Error>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let service = Rc::clone(&self.service);
        let path = req.path().to_string();
        let requires_auth = self.requires_auth(&path);

        Box::pin(async move {
            if !requires_auth {
                debug!("Route publique autorisée: {}", path);
                return service.call(req).await;
            }

            debug!("Vérification d'authentification pour: {}", path);

            // Récupérer la session
            let session = req.get_session();
            
            // Vérifier le token JWT en session
            let token = match session.get::<String>("jwt") {
                Ok(Some(token)) => token,
                Ok(None) => {
                    warn!("Accès refusé à {} - Aucun token en session", path);
                    return Ok(create_unauthorized_response("Authentification requise"));
                }
                Err(e) => {
                    error!("Erreur lors de la récupération de session pour {}: {}", path, e);
                    return Ok(create_unauthorized_response("Erreur de session"));
                }
            };

            // Récupérer l'adresse IP pour validation
            let ip_address = req.connection_info().realip_remote_addr()
                .unwrap_or("127.0.0.1");

            // Valider le token JWT
            match validate_advanced_jwt(&token, Some(ip_address), None) {
                Ok(claims) => {
                    debug!("Authentification réussie pour {} - Utilisateur: {}", path, claims.sub);
                    
                    // Ajouter les informations utilisateur à la requête
                    req.extensions_mut().insert(AuthenticatedUser {
                        id: claims.jti.clone(),
                        username: claims.sub.clone(),
                        role: claims.role.clone(),
                        permissions: claims.permissions.clone(),
                        session_id: claims.session_id.clone(),
                    });

                    // Continuer avec la requête
                    service.call(req).await
                }
                Err(e) => {
                    warn!("Token invalide pour {} depuis {}: {}", path, ip_address, e);
                    
                    // Nettoyer la session invalide
                    session.remove("jwt");
                    session.remove("user_id");
                    
                    Ok(create_unauthorized_response("Token invalide ou expiré"))
                }
            }
        })
    }
}

/// Informations de l'utilisateur authentifié
#[derive(Debug, Clone)]
pub struct AuthenticatedUser {
    pub id: String,
    pub username: String,
    pub role: String,
    pub permissions: Vec<String>,
    pub session_id: String,
}

/// Créer une réponse d'erreur d'authentification
fn create_unauthorized_response<B>(message: &str) -> ServiceResponse<B> {
    let response = HttpResponse::Unauthorized()
        .json(json!({
            "error": "Unauthorized",
            "message": message,
            "redirect": "/login"
        }))
        .map_into_boxed_body()
        .map_into_right_body();

    ServiceResponse::new(
        actix_web::dev::ServiceRequest::from_parts(
            actix_web::HttpRequest::default(),
            actix_web::dev::Payload::None,
        ).0,
        response,
    )
}

/// Extension pour récupérer l'utilisateur authentifié depuis une requête
pub trait AuthenticatedUserExt {
    fn authenticated_user(&self) -> Option<&AuthenticatedUser>;
}

impl AuthenticatedUserExt for ServiceRequest {
    fn authenticated_user(&self) -> Option<&AuthenticatedUser> {
        self.extensions().get::<AuthenticatedUser>()
    }
}

impl AuthenticatedUserExt for actix_web::HttpRequest {
    fn authenticated_user(&self) -> Option<&AuthenticatedUser> {
        self.extensions().get::<AuthenticatedUser>()
    }
}

/// Middleware pour vérifier les permissions spécifiques
pub struct PermissionMiddleware {
    required_permission: String,
}

impl PermissionMiddleware {
    pub fn new(permission: String) -> Self {
        Self {
            required_permission: permission,
        }
    }
}

impl<S, B> Transform<S, ServiceRequest> for PermissionMiddleware
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type InitError = ();
    type Transform = PermissionMiddlewareService<S>;
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(PermissionMiddlewareService {
            service: Rc::new(service),
            required_permission: self.required_permission.clone(),
        }))
    }
}

pub struct PermissionMiddlewareService<S> {
    service: Rc<S>,
    required_permission: String,
}

impl<S, B> Service<ServiceRequest> for PermissionMiddlewareService<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = LocalBoxFuture<'static, Result<Self::Response, Self::Error>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let service = Rc::clone(&self.service);
        let required_permission = self.required_permission.clone();

        Box::pin(async move {
            // Vérifier si l'utilisateur est authentifié
            let user = match req.authenticated_user() {
                Some(user) => user,
                None => {
                    warn!("Accès refusé - Utilisateur non authentifié");
                    return Ok(create_unauthorized_response("Authentification requise"));
                }
            };

            // Vérifier les permissions
            if user.permissions.contains(&required_permission) || user.role == "admin" {
                debug!("Permission accordée pour {} - Utilisateur: {}", required_permission, user.username);
                service.call(req).await
            } else {
                warn!("Permission refusée pour {} - Utilisateur: {} (permissions: {:?})", 
                      required_permission, user.username, user.permissions);
                Ok(create_unauthorized_response("Permissions insuffisantes"))
            }
        })
    }
}
