use std::env;
use std::path::PathBuf;

/// Configuration centralisée pour l'application de gestion des visiteurs
#[derive(Debug, Clone)]
pub struct AppConfig {
    pub app_name: String,
    pub app_env: String,
    pub app_debug: bool,
    pub app_url: String,
    pub ssl: SslConfig,
    pub security: SecurityConfig,
    pub database: DatabaseConfig,
    pub logging: LoggingConfig,
}

/// Configuration SSL/TLS
#[derive(Debug, Clone)]
pub struct SslConfig {
    pub cert_path: PathBuf,
    pub key_path: PathBuf,
    pub port: u16,
    pub https_only: bool,
    pub hsts_max_age: u32,
    pub hsts_include_subdomains: bool,
    pub hsts_preload: bool,
}

/// Configuration de sécurité
#[derive(Debug, Clone)]
pub struct SecurityConfig {
    pub csrf_secret: String,
    pub session_secret: String,
    pub jwt_secret: String,
    pub rate_limit_requests: u32,
    pub rate_limit_window: u64,
    pub rate_limit_block_duration: u64,
}

/// Configuration de la base de données
#[derive(Debug, Clone)]
pub struct DatabaseConfig {
    pub url: String,
    pub ssl_mode: String,
    pub max_connections: u32,
}

/// Configuration des logs
#[derive(Debug, Clone)]
pub struct LoggingConfig {
    pub level: String,
    pub path: PathBuf,
}

impl AppConfig {
    /// Charger la configuration depuis les variables d'environnement
    pub fn from_env() -> Result<Self, Box<dyn std::error::Error>> {
        Ok(AppConfig {
            app_name: env::var("APP_NAME").unwrap_or_else(|_| "app_gestion_visiteurs".to_string()),
            app_env: env::var("APP_ENV").unwrap_or_else(|_| "development".to_string()),
            app_debug: env::var("APP_DEBUG").unwrap_or_else(|_| "false".to_string()).parse()?,
            app_url: env::var("APP_URL").unwrap_or_else(|_| "https://localhost:8443".to_string()),
            
            ssl: SslConfig {
                cert_path: PathBuf::from(env::var("SSL_CERT_PATH").unwrap_or_else(|_| "./certs/localhost.pem".to_string())),
                key_path: PathBuf::from(env::var("SSL_KEY_PATH").unwrap_or_else(|_| "./certs/localhost-key.pem".to_string())),
                port: env::var("SSL_PORT").unwrap_or_else(|_| "8443".to_string()).parse()?,
                https_only: env::var("HTTPS_ONLY").unwrap_or_else(|_| "true".to_string()).parse()?,
                hsts_max_age: env::var("HSTS_MAX_AGE").unwrap_or_else(|_| "31536000".to_string()).parse()?,
                hsts_include_subdomains: env::var("HSTS_INCLUDE_SUBDOMAINS").unwrap_or_else(|_| "true".to_string()).parse()?,
                hsts_preload: env::var("HSTS_PRELOAD").unwrap_or_else(|_| "true".to_string()).parse()?,
            },
            
            security: SecurityConfig {
                csrf_secret: env::var("CSRF_SECRET").unwrap_or_else(|_| {
                    log::warn!("CSRF_SECRET non défini, génération d'une clé temporaire");
                    generate_random_key()
                }),
                session_secret: env::var("SESSION_SECRET").unwrap_or_else(|_| {
                    log::warn!("SESSION_SECRET non défini, génération d'une clé temporaire");
                    generate_random_key()
                }),
                jwt_secret: env::var("JWT_SECRET").unwrap_or_else(|_| {
                    log::warn!("JWT_SECRET non défini, génération d'une clé temporaire");
                    generate_random_key()
                }),
                rate_limit_requests: env::var("RATE_LIMIT_REQUESTS").unwrap_or_else(|_| "100".to_string()).parse()?,
                rate_limit_window: env::var("RATE_LIMIT_WINDOW").unwrap_or_else(|_| "60".to_string()).parse()?,
                rate_limit_block_duration: env::var("RATE_LIMIT_BLOCK_DURATION").unwrap_or_else(|_| "300".to_string()).parse()?,
            },
            
            database: DatabaseConfig {
                url: env::var("DATABASE_URL").unwrap_or_else(|_| "postgresql://localhost/app_gestion_visiteurs".to_string()),
                ssl_mode: env::var("DATABASE_SSL_MODE").unwrap_or_else(|_| "prefer".to_string()),
                max_connections: env::var("DATABASE_MAX_CONNECTIONS").unwrap_or_else(|_| "10".to_string()).parse()?,
            },
            
            logging: LoggingConfig {
                level: env::var("LOG_LEVEL").unwrap_or_else(|_| "info".to_string()),
                path: PathBuf::from(env::var("LOG_PATH").unwrap_or_else(|_| "./logs/app.log".to_string())),
            },
        })
    }
    
    /// Vérifier si l'application est en mode production
    pub fn is_production(&self) -> bool {
        self.app_env == "production"
    }
    
    /// Vérifier si l'application est en mode développement
    pub fn is_development(&self) -> bool {
        self.app_env == "development"
    }
    
    /// Obtenir l'adresse de bind pour le serveur
    pub fn bind_address(&self) -> String {
        if self.is_production() {
            format!("0.0.0.0:{}", self.ssl.port)
        } else {
            format!("127.0.0.1:{}", self.ssl.port)
        }
    }
}

impl Default for AppConfig {
    fn default() -> Self {
        AppConfig {
            app_name: "app_gestion_visiteurs".to_string(),
            app_env: "development".to_string(),
            app_debug: true,
            app_url: "http://localhost:8080".to_string(),

            ssl: SslConfig {
                cert_path: PathBuf::from("./certs/localhost.pem"),
                key_path: PathBuf::from("./certs/localhost-key.pem"),
                port: 8080,
                https_only: false,
                hsts_max_age: 31536000,
                hsts_include_subdomains: false,
                hsts_preload: false,
            },

            security: SecurityConfig {
                csrf_secret: generate_random_key(),
                session_secret: generate_random_key(),
                jwt_secret: generate_random_key(),
                rate_limit_requests: 100,
                rate_limit_window: 60,
                rate_limit_block_duration: 300,
            },

            database: DatabaseConfig {
                url: "postgresql://localhost/app_gestion_visiteurs".to_string(),
                ssl_mode: "prefer".to_string(),
                max_connections: 10,
            },

            logging: LoggingConfig {
                level: "info".to_string(),
                path: PathBuf::from("./logs/app.log"),
            },
        }
    }
}

/// Générer une clé aléatoire sécurisée
fn generate_random_key() -> String {
    use rand::Rng;
    let mut rng = rand::thread_rng();
    let key: [u8; 32] = rng.gen();
    hex::encode(key)
}

/// Fonctions utilitaires pour la compatibilité
pub fn jwt_secret() -> String {
    env::var("JWT_SECRET").unwrap_or_else(|_| {
        log::warn!("JWT_SECRET non défini, génération d'une clé temporaire");
        generate_random_key()
    })
}

pub fn csrf_secret() -> String {
    env::var("CSRF_SECRET").unwrap_or_else(|_| {
        log::warn!("CSRF_SECRET non défini, génération d'une clé temporaire");
        generate_random_key()
    })
}

pub fn session_secret() -> String {
    env::var("SESSION_SECRET").unwrap_or_else(|_| {
        log::warn!("SESSION_SECRET non défini, génération d'une clé temporaire");
        generate_random_key()
    })
}

