use actix_web::{App, HttpServer, middleware::Logger, web, Result};
use actix_files as fs;
use actix_cors::Cors;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
// Temporairement désactivé pour éviter les erreurs OpenSSL
// use metrics_exporter_prometheus::PrometheusBuilder;
// Modules de l'application
mod routes;
mod security;
mod session;
mod config;
mod utils;
// Temporairement désactivé pour compilation
// mod middleware;
mod monitoring;
mod handlers;
mod models;

// Imports des modules
use crate::routes::{auth::auth_routes, web::web_routes};
use crate::session::session_middleware;
use crate::security::{
    SecurityMiddleware,
    headers::SecurityHeaders,
    rate_limiting::RateLimitMiddleware,
    csrf::CsrfProtection,
};
use crate::config::AppConfig;
// Temporairement désactivé pour compilation
// use crate::middleware::{AdvancedSecurityMiddleware, AdvancedSecurityConfig};
use crate::monitoring::{get_metrics_manager, get_health_metrics};

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    // Initialisation avancée du logging avec tracing
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "dcop413=debug,actix_web=info".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    // Initialisation de l'environnement
    dotenv::dotenv().ok();

    // Métriques Prometheus temporairement désactivées
    // PrometheusBuilder::new()
    //     .with_http_listener(([127, 0, 0, 1], 9090))
    //     .install()
    //     .expect("Impossible d'initialiser les métriques Prometheus");

    // Charger la configuration
    println!("🔧 Chargement de la configuration...");
    let config = match AppConfig::from_env() {
        Ok(config) => {
            println!("✅ Configuration chargée avec succès");
            config
        },
        Err(e) => {
            println!("❌ Erreur lors du chargement de la configuration: {}", e);
            println!("🔧 Utilisation de la configuration par défaut");
            AppConfig::default()
        }
    };
    let config = std::sync::Arc::new(config);

    println!("🚀 Démarrage de {} en mode {}", config.app_name, config.app_env);
    println!("📊 Métriques Prometheus disponibles sur http://127.0.0.1:9090/metrics");

    // Initialiser le gestionnaire de métriques
    println!("🔧 Initialisation du gestionnaire de métriques...");
    let _metrics_manager = get_metrics_manager();
    println!("📈 Gestionnaire de métriques initialisé");

    // Configuration de sécurité avancée (temporairement désactivée)
    // let security_config = AdvancedSecurityConfig {
    //     rate_limit_requests_per_minute: 100,
    //     rate_limit_burst: 10,
    //     enable_csrf_protection: true,
    //     enable_xss_protection: true,
    //     enable_sql_injection_detection: true,
    //     enable_suspicious_ua_blocking: true,
    //     max_request_size: 10 * 1024 * 1024, // 10MB
    //     blocked_ips: vec![],
    //     allowed_origins: vec![
    //         "http://localhost:8443".to_string(),
    //         "https://dcop413.local".to_string(),
    //     ],
    // };

    // Configurer et démarrer le serveur
    println!("🔧 Configuration du serveur...");
    let bind_address = config.bind_address();
    println!("🌐 Démarrage du serveur ultra-sécurisé sur {}", bind_address);
    println!("🛡️ Protection OWASP Top 10 activée");
    println!("🔐 Chiffrement AES-256 activé pour les données sensibles");
    println!("⚡ Rate limiting: 100 req/min avec burst de 10");
    println!("🔍 Détection XSS et SQL injection activée");

    // Démarrer le serveur ultra-sécurisé
    println!("🔧 Création du serveur HTTP...");
    let config_clone = config.clone();
    // let security_config_clone = security_config.clone();

    println!("🔧 Configuration des middlewares...");
    let server = HttpServer::new(move || {
        // Configuration de la sécurité selon l'environnement
        let security_headers = if config_clone.is_development() {
            tracing::info!("🔧 Mode développement - Headers de sécurité allégés");
            SecurityHeaders::development()
        } else {
            tracing::info!("🔒 Mode production - Headers de sécurité complets OWASP");
            SecurityHeaders::production()
        };

        // Middleware de sécurité avancé (temporairement désactivé pour compilation)
        // let advanced_security = AdvancedSecurityMiddleware::new(security_config_clone.clone());

        // Configuration CORS sécurisée
        let cors = Cors::default()
            .allowed_origin("http://localhost:8443")
            .allowed_origin("http://127.0.0.1:8443")
            .allowed_origin("https://dcop413.local")
            .allowed_methods(vec!["GET", "POST", "PUT", "DELETE"])
            .allowed_headers(vec![
                actix_web::http::header::AUTHORIZATION,
                actix_web::http::header::ACCEPT,
                actix_web::http::header::CONTENT_TYPE,
                actix_web::http::header::HeaderName::from_static("x-csrf-token"),
            ])
            .max_age(3600);

        // Configuration du rate limiting legacy
        let rate_limit_config = security::rate_limiting::RateLimitConfig {
            requests_per_window: config_clone.security.rate_limit_requests,
            window_duration: std::time::Duration::from_secs(config_clone.security.rate_limit_window),
            block_duration: std::time::Duration::from_secs(config_clone.security.rate_limit_block_duration),
            cleanup_interval: std::time::Duration::from_secs(3600),
        };

        // Configuration CSRF avec la clé de la configuration
        let csrf_protection = CsrfProtection::new(&config_clone.security.csrf_secret);

        // Headers de sécurité OWASP complets
        let security_headers_middleware = actix_web::middleware::DefaultHeaders::new()
            .add(("X-Content-Type-Options", "nosniff"))
            .add(("X-Frame-Options", "DENY"))
            .add(("X-XSS-Protection", "1; mode=block"))
            .add(("Strict-Transport-Security", "max-age=31536000; includeSubDomains"))
            .add(("Content-Security-Policy", "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com; style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com; img-src 'self' data:; font-src 'self'"))
            .add(("Referrer-Policy", "strict-origin-when-cross-origin"))
            .add(("Permissions-Policy", "geolocation=(), microphone=(), camera=()"))
            .add(("X-Permitted-Cross-Domain-Policies", "none"))
            .add(("Cross-Origin-Embedder-Policy", "require-corp"))
            .add(("Cross-Origin-Opener-Policy", "same-origin"))
            .add(("Cross-Origin-Resource-Policy", "same-origin"));

        App::new()
            // Middleware de logging avancé avec tracing
            .wrap(tracing_actix_web::TracingLogger::default())

            // Middleware de sécurité avancé (temporairement désactivé)
            // .wrap(advanced_security)

            // Headers de sécurité OWASP
            .wrap(security_headers_middleware)

            // CORS sécurisé
            .wrap(cors)

            // Middleware de sécurité legacy (compatibilité)
            .wrap(SecurityMiddleware)
            .wrap(security_headers)
            .wrap(RateLimitMiddleware::new(rate_limit_config))
            .wrap(csrf_protection)

            // Sessions sécurisées
            .wrap(session_middleware())

            // Routes de l'application
            .configure(web_routes)
            .configure(auth_routes)

            // Route de métriques et santé (accès restreint)
            .route("/metrics", web::get().to(metrics_handler))
            .route("/health", web::get().to(health_handler))

            // Servir les fichiers statiques de manière sécurisée et automatique
            .service(
                fs::Files::new("/css", "./frontend/css")
                    .use_etag(true)
                    .use_last_modified(true)
                    .prefer_utf8(true)
            )
            .service(
                fs::Files::new("/js", "./frontend/js_ts")
                    .use_etag(true)
                    .use_last_modified(true)
                    .prefer_utf8(true)
            )
            .service(
                fs::Files::new("/images", "./imgs")
                    .use_etag(true)
                    .use_last_modified(true)
            )
            // Route catch-all pour servir les fichiers frontend automatiquement
            .service(
                fs::Files::new("/", "./frontend")
                    .use_etag(true)
                    .use_last_modified(true)
                    .prefer_utf8(true)
                    .index_file("html/login.html")  // Page par défaut sécurisée
            )
    });

    println!("🔧 Liaison du serveur à l'adresse {}...", bind_address);
    let server = server.bind(&bind_address)?;
    println!("✅ Serveur lié avec succès à {}", bind_address);
    println!("🚀 Démarrage du serveur...");

    // Démarrer le serveur de manière asynchrone
    let server_future = server.run();
    println!("✅ Serveur DCOP 413 démarré avec succès !");
    println!("🌐 Application disponible sur : http://{}", bind_address);
    println!("📊 Métriques disponibles sur : http://127.0.0.1:9090/metrics");
    println!("🛡️ Sécurité : OWASP Top 10, Rate Limiting, Chiffrement AES-256");
    println!("📝 Logs : Surveillance en temps réel activée");
    println!("🔄 Prêt à recevoir des connexions...");

    server_future.await
}

/// Handler pour les métriques Prometheus
async fn metrics_handler() -> Result<actix_web::HttpResponse> {
    let metrics = get_metrics_manager().get_metrics();
    Ok(actix_web::HttpResponse::Ok().json(metrics))
}

/// Handler pour le health check
async fn health_handler() -> Result<actix_web::HttpResponse> {
    let health = get_health_metrics();
    Ok(actix_web::HttpResponse::Ok().json(health))
}
