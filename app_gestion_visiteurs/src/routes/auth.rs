use actix_web::{web, HttpResponse, HttpRequest, post, get, Result};
use actix_web::Responder;
use actix_session::Session;
use serde::{Deserialize, Serialize};
use serde_json::json;
use anyhow::anyhow;
use tracing::{info, warn, error};

// Temporairement désactivé pour compilation sans base de données
// use crate::database::users::{UserRepository, LoginCredentials, AuthResult, CreateUserRequest};
// use sqlx::PgPool;
use crate::utils::advanced_jwt::{create_advanced_jwt, validate_advanced_jwt};

/// Réponse d'authentification standardisée
#[derive(Serialize)]
pub struct AuthResponse {
    pub success: bool,
    pub message: String,
    pub token: Option<String>,
    pub user: Option<UserInfo>,
    pub locked_until: Option<chrono::DateTime<chrono::Utc>>,
}

/// Informations utilisateur sécurisées (sans données sensibles)
#[derive(Serialize)]
pub struct UserInfo {
    pub id: String,
    pub username: String,
    pub email: String,
    pub role: String,
    pub last_login: Option<chrono::DateTime<chrono::Utc>>,
}

/// Requête de création d'utilisateur
#[derive(Deserialize)]
pub struct CreateUserDto {
    pub username: String,
    pub email: String,
    pub password: String,
    pub role: Option<String>,
}

/// Route de connexion sécurisée
#[post("/login")]
async fn login(
    creds: web::Json<LoginCredentials>,
    session: Session,
    req: HttpRequest,
    pool: web::Data<PgPool>
) -> Result<impl Responder> {
    let ip_address = req.connection_info().realip_remote_addr()
        .unwrap_or("127.0.0.1").to_string();
    let user_repo = UserRepository::new(pool.get_ref().clone());

    info!("Tentative de connexion pour: {} depuis {}", creds.username, ip_address);

    // Extraire les credentials avant de les utiliser
    let credentials = creds.into_inner();
    let username = credentials.username.clone();

    // Authentifier l'utilisateur
    match user_repo.authenticate_user(credentials, &ip_address).await {
        Ok(auth_result) => {
            if auth_result.success {
                // Stocker le token en session
                if let Some(ref token) = auth_result.token {
                    session.insert("jwt", token)?;
                    session.insert("user_id", auth_result.user.as_ref().unwrap().id.to_string())?;
                }

                let response = AuthResponse {
                    success: true,
                    message: auth_result.message,
                    token: auth_result.token,
                    user: auth_result.user.map(|u| UserInfo {
                        id: u.id.to_string(),
                        username: u.username,
                        email: u.email,
                        role: u.role,
                        last_login: u.last_login,
                    }),
                    locked_until: None,
                };

                Ok(HttpResponse::Ok().json(response))
            } else {
                warn!("Échec de connexion pour: {} depuis {}", username, ip_address);

                let response = AuthResponse {
                    success: false,
                    message: auth_result.message,
                    token: None,
                    user: None,
                    locked_until: auth_result.locked_until,
                };

                Ok(HttpResponse::Unauthorized().json(response))
            }
        }
        Err(e) => {
            error!("Erreur lors de l'authentification: {}", e);

            let response = AuthResponse {
                success: false,
                message: "Erreur interne du serveur".to_string(),
                token: None,
                user: None,
                locked_until: None,
            };

            Ok(HttpResponse::InternalServerError().json(response))
        }
    }
}

/// Route de vérification d'authentification
#[get("/verify")]
async fn verify_auth(
    session: Session,
    req: HttpRequest,
    pool: web::Data<PgPool>
) -> Result<impl Responder> {
    // Récupérer le token de la session
    let token = match session.get::<String>("jwt")? {
        Some(token) => token,
        None => {
            return Ok(HttpResponse::Unauthorized().json(json!({
                "authenticated": false,
                "message": "Aucun token trouvé"
            })));
        }
    };

    let ip_address = req.connection_info().realip_remote_addr()
        .unwrap_or("127.0.0.1").to_string();

    // Valider le token JWT avancé
    match validate_advanced_jwt(&token, Some(&ip_address), None) {
        Ok(claims) => {
            // Vérifier que l'utilisateur existe toujours
            let user_repo = UserRepository::new(pool.get_ref().clone());
            match user_repo.get_user_by_username(&claims.sub).await {
                Ok(Some(user)) => {
                    if user.is_active {
                        Ok(HttpResponse::Ok().json(json!({
                            "authenticated": true,
                            "message": "Token valide",
                            "user": UserInfo {
                                id: user.id.to_string(),
                                username: user.username,
                                email: user.email,
                                role: user.role,
                                last_login: user.last_login,
                            }
                        })))
                    } else {
                        session.remove("jwt");
                        session.remove("user_id");
                        Ok(HttpResponse::Unauthorized().json(json!({
                            "authenticated": false,
                            "message": "Compte désactivé"
                        })))
                    }
                }
                Ok(None) => {
                    session.remove("jwt");
                    session.remove("user_id");
                    Ok(HttpResponse::Unauthorized().json(json!({
                        "authenticated": false,
                        "message": "Utilisateur introuvable"
                    })))
                }
                Err(e) => {
                    error!("Erreur lors de la vérification utilisateur: {}", e);
                    Ok(HttpResponse::InternalServerError().json(json!({
                        "authenticated": false,
                        "message": "Erreur interne"
                    })))
                }
            }
        }
        Err(e) => {
            warn!("Token invalide depuis {}: {}", ip_address, e);
            session.remove("jwt");
            session.remove("user_id");
            Ok(HttpResponse::Unauthorized().json(json!({
                "authenticated": false,
                "message": "Token invalide ou expiré"
            })))
        }
    }
}

/// Route de déconnexion sécurisée
#[post("/logout")]
async fn logout(
    session: Session,
    req: HttpRequest
) -> Result<impl Responder> {
    let ip_address = req.connection_info().realip_remote_addr()
        .unwrap_or("127.0.0.1").to_string();

    // Récupérer les informations de session avant de les supprimer
    let _user_id = session.get::<String>("user_id").unwrap_or_default();
    let token = session.get::<String>("jwt").unwrap_or_default();

    // Supprimer la session
    session.clear();

    if let Some(token) = token {
        // Révoquer le token JWT si possible
        if let Ok(claims) = validate_advanced_jwt(&token, Some(&ip_address), None) {
            crate::utils::advanced_jwt::revoke_token(&claims.jti);
            info!("Déconnexion réussie pour utilisateur {} depuis {}", claims.sub, ip_address);
        }
    }

    Ok(HttpResponse::Ok().json(json!({
        "success": true,
        "message": "Déconnexion réussie"
    })))
}

/// Route pour créer un nouvel utilisateur (admin seulement)
#[post("/create-user")]
async fn create_user(
    user_data: web::Json<CreateUserDto>,
    session: Session,
    pool: web::Data<PgPool>
) -> Result<impl Responder> {
    // Vérifier que l'utilisateur connecté est admin
    let token = match session.get::<String>("jwt")? {
        Some(token) => token,
        None => {
            return Ok(HttpResponse::Unauthorized().json(json!({
                "success": false,
                "message": "Authentification requise"
            })));
        }
    };

    let claims = match validate_advanced_jwt(&token, None, None) {
        Ok(claims) => claims,
        Err(_) => {
            return Ok(HttpResponse::Unauthorized().json(json!({
                "success": false,
                "message": "Token invalide"
            })));
        }
    };

    if claims.role != "admin" {
        return Ok(HttpResponse::Forbidden().json(json!({
            "success": false,
            "message": "Permissions insuffisantes"
        })));
    }

    // Créer l'utilisateur
    let user_repo = UserRepository::new(pool.get_ref().clone());
    let create_request = CreateUserRequest {
        username: user_data.username.clone(),
        email: user_data.email.clone(),
        password: user_data.password.clone(),
        role: user_data.role.clone().unwrap_or_else(|| "user".to_string()),
    };

    match user_repo.create_user(create_request, Some(uuid::Uuid::parse_str(&claims.sub).unwrap_or_default())).await {
        Ok(user) => {
            info!("Nouvel utilisateur créé: {} par {}", user.username, claims.sub);
            Ok(HttpResponse::Created().json(json!({
                "success": true,
                "message": "Utilisateur créé avec succès",
                "user": UserInfo {
                    id: user.id.to_string(),
                    username: user.username,
                    email: user.email,
                    role: user.role,
                    last_login: user.last_login,
                }
            })))
        }
        Err(e) => {
            error!("Erreur lors de la création d'utilisateur: {}", e);
            Ok(HttpResponse::BadRequest().json(json!({
                "success": false,
                "message": format!("Erreur: {}", e)
            })))
        }
    }
}

/// Configuration des routes d'authentification
pub fn auth_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(login)
       .service(verify_auth)
       .service(logout)
       .service(create_user);
}

