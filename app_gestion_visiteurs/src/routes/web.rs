use actix_web::{web, HttpResponse, get, post, Result};
use actix_files::NamedFile;
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use std::collections::HashMap;
use crate::security::{
    validation::validate_visitor_form,
    input_sanitization::{sanitize_form_data, detect_attack_patterns},
    secure_log,
};

// Structure pour recevoir les données du formulaire (non validées)
#[derive(Deserialize, Debug)]
pub struct RawVisitorForm {
    pub nom: Option<String>,
    pub postnom: Option<String>,
    pub prenom: Option<String>,
    pub sexe: Option<String>,
    pub organisation: Option<String>,
    pub fonction: Option<String>,
    pub telephone1: Option<String>,
    pub telephone2: Option<String>,
    pub telephone3: Option<String>,
    pub email: Option<String>,
    pub motif: Option<String>,
    pub engagement: Option<String>,
    pub date: Option<String>,
    pub autorisation: Option<String>,
    pub photo_data: Option<String>,
    pub csrf_token: Option<String>,
}

// Structure pour la réponse sécurisée
#[derive(Serialize)]
pub struct SecureFormResponse {
    pub success: bool,
    pub message: String,
    pub visitor_id: Option<String>, // UUID au lieu d'entier
    pub errors: Option<Vec<String>>,
    pub security_warnings: Option<Vec<String>>,
}

// Route racine - redirection vers la page de connexion
#[get("/")]
pub async fn root() -> Result<HttpResponse> {
    println!("🔄 Route racine appelée - redirection vers /login");
    Ok(HttpResponse::Found()
        .append_header(("Location", "/login"))
        .finish())
}

// Route pour la page d'accueil (après connexion)
#[get("/accueil")]
async fn accueil() -> Result<NamedFile> {
    let path = PathBuf::from("./frontend/html/accueil.html");
    println!("Tentative d'ouverture du fichier: {:?}", path);
    match NamedFile::open(&path) {
        Ok(file) => Ok(file),
        Err(e) => {
            println!("Erreur lors de l'ouverture du fichier {:?}: {}", path, e);
            Err(actix_web::error::ErrorNotFound("Page non trouvée"))
        }
    }
}

// Route pour la page principale du formulaire
#[get("/formulaire")]
async fn formulaire() -> Result<NamedFile> {
    let path = PathBuf::from("./frontend/html/main.html");
    println!("Tentative d'ouverture du fichier: {:?}", path);
    match NamedFile::open(&path) {
        Ok(file) => Ok(file),
        Err(e) => {
            println!("Erreur lors de l'ouverture du fichier {:?}: {}", path, e);
            Err(actix_web::error::ErrorNotFound("Page non trouvée"))
        }
    }
}

// Route pour la page de capture photo
#[get("/photo")]
async fn photo() -> Result<NamedFile> {
    let path = PathBuf::from("./frontend/html/photo.html");
    println!("Tentative d'ouverture du fichier: {:?}", path);
    match NamedFile::open(&path) {
        Ok(file) => Ok(file),
        Err(e) => {
            println!("Erreur lors de l'ouverture du fichier {:?}: {}", path, e);
            Err(actix_web::error::ErrorNotFound("Page non trouvée"))
        }
    }
}

// Route pour la page de connexion
#[get("/login")]
async fn login_page() -> Result<NamedFile> {
    let path = PathBuf::from("./frontend/html/login.html");
    println!("Tentative d'ouverture du fichier: {:?}", path);
    match NamedFile::open(&path) {
        Ok(file) => Ok(file),
        Err(e) => {
            println!("Erreur lors de l'ouverture du fichier {:?}: {}", path, e);
            Err(actix_web::error::ErrorNotFound("Page non trouvée"))
        }
    }
}

// Route pour la déconnexion
#[get("/logout")]
async fn logout() -> Result<HttpResponse> {
    Ok(HttpResponse::Found()
        .append_header(("Location", "/login"))
        .json(serde_json::json!({
            "success": true,
            "message": "Déconnexion réussie"
        })))
}

// Route sécurisée pour traiter la soumission du formulaire
#[post("/submit-visitor")]
async fn submit_visitor(
    form: web::Json<RawVisitorForm>,
    req: actix_web::HttpRequest,
) -> Result<HttpResponse> {
    // Extraire l'IP du client pour le logging
    let client_ip = req.connection_info().peer_addr().unwrap_or("unknown").to_string();

    secure_log(
        log::Level::Info,
        "Visitor form submission attempt",
        Some(&client_ip),
    );

    // Convertir en HashMap pour la validation
    let mut form_data = HashMap::new();

    // Ajouter tous les champs avec des valeurs par défaut
    form_data.insert("nom".to_string(), form.nom.clone().unwrap_or_default());
    form_data.insert("postnom".to_string(), form.postnom.clone().unwrap_or_default());
    form_data.insert("prenom".to_string(), form.prenom.clone().unwrap_or_default());
    form_data.insert("sexe".to_string(), form.sexe.clone().unwrap_or_default());
    form_data.insert("organisation".to_string(), form.organisation.clone().unwrap_or_default());
    form_data.insert("fonction".to_string(), form.fonction.clone().unwrap_or_default());
    form_data.insert("telephone1".to_string(), form.telephone1.clone().unwrap_or_default());
    form_data.insert("telephone2".to_string(), form.telephone2.clone().unwrap_or_default());
    form_data.insert("telephone3".to_string(), form.telephone3.clone().unwrap_or_default());
    form_data.insert("email".to_string(), form.email.clone().unwrap_or_default());
    form_data.insert("motif".to_string(), form.motif.clone().unwrap_or_default());
    form_data.insert("engagement".to_string(), form.engagement.clone().unwrap_or_default());
    form_data.insert("date".to_string(), form.date.clone().unwrap_or_default());
    form_data.insert("autorisation".to_string(), form.autorisation.clone().unwrap_or_default());
    form_data.insert("photo_data".to_string(), form.photo_data.clone().unwrap_or_default());

    // Détecter les tentatives d'attaque
    let mut security_warnings = Vec::new();
    for (field, value) in &form_data {
        let threats = detect_attack_patterns(value);
        if !threats.is_empty() {
            security_warnings.extend(threats.iter().map(|t| format!("Field '{}': {}", field, t)));
            secure_log(
                log::Level::Warn,
                &format!("Security threat detected in field '{}'", field),
                Some(&client_ip),
            );
        }
    }

    // Sanitiser les données
    let sanitized_data = sanitize_form_data(&form_data);

    // Valider les données sanitisées
    match validate_visitor_form(&sanitized_data) {
        Ok(_validated_form) => {
            // Générer un UUID sécurisé pour le visiteur
            let visitor_id = uuid::Uuid::new_v4().to_string();

            secure_log(
                log::Level::Info,
                &format!("Visitor form validated successfully. ID: {}", visitor_id),
                Some(&client_ip),
            );

            // TODO: Sauvegarder en base de données avec les données validées
            // save_visitor_to_database(&validated_form, &visitor_id).await?;

            let response = SecureFormResponse {
                success: true,
                message: "Fiche de réception enregistrée avec succès!".to_string(),
                visitor_id: Some(visitor_id),
                errors: None,
                security_warnings: if security_warnings.is_empty() { None } else { Some(security_warnings) },
            };

            Ok(HttpResponse::Ok().json(response))
        },
        Err(validation_errors) => {
            secure_log(
                log::Level::Warn,
                "Visitor form validation failed",
                Some(&client_ip),
            );

            let response = SecureFormResponse {
                success: false,
                message: "Erreurs de validation détectées".to_string(),
                visitor_id: None,
                errors: Some(validation_errors),
                security_warnings: if security_warnings.is_empty() { None } else { Some(security_warnings) },
            };

            Ok(HttpResponse::BadRequest().json(response))
        }
    }
}

// Les fichiers statiques sont maintenant servis par actix_files::Files dans main.rs

// Configuration des routes web
pub fn web_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(root)
       .service(accueil)
       .service(formulaire)
       .service(photo)
       .service(login_page)
       .service(logout)
       .service(submit_visitor)
       // Les fichiers statiques sont servis par actix_files::Files dans main.rs
       ;
}
