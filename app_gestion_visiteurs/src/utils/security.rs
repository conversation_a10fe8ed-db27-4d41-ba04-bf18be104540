use actix_web::{HttpRequest, HttpResponse, Result};
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use tracing::{warn, error, info};

#[derive(Debug, Serialize, Deserialize)]
pub struct SecurityEvent {
    pub event_type: String,
    pub severity: String,
    pub ip_address: String,
    pub user_agent: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub details: HashMap<String, String>,
}

pub fn log_security_event(
    event_type: &str,
    severity: &str,
    req: &HttpRequest,
    details: HashMap<String, String>,
) {
    let ip_address = get_client_ip(req);
    let user_agent = get_user_agent(req);
    
    let event = SecurityEvent {
        event_type: event_type.to_string(),
        severity: severity.to_string(),
        ip_address,
        user_agent,
        timestamp: chrono::Utc::now(),
        details,
    };
    
    match severity {
        "HIGH" | "CRITICAL" => error!("Security Event: {:?}", event),
        "MEDIUM" => warn!("Security Event: {:?}", event),
        _ => info!("Security Event: {:?}", event),
    }
}

pub fn get_client_ip(req: &HttpRequest) -> String {
    // Vérifier les headers de proxy
    if let Some(forwarded_for) = req.headers().get("X-Forwarded-For") {
        if let Ok(forwarded_str) = forwarded_for.to_str() {
            if let Some(first_ip) = forwarded_str.split(',').next() {
                return first_ip.trim().to_string();
            }
        }
    }
    
    if let Some(real_ip) = req.headers().get("X-Real-IP") {
        if let Ok(real_ip_str) = real_ip.to_str() {
            return real_ip_str.to_string();
        }
    }
    
    // IP de connexion directe
    req.connection_info()
        .peer_addr()
        .unwrap_or("unknown")
        .to_string()
}

pub fn get_user_agent(req: &HttpRequest) -> String {
    req.headers()
        .get("User-Agent")
        .and_then(|ua| ua.to_str().ok())
        .unwrap_or("unknown")
        .to_string()
}

pub fn is_suspicious_user_agent(user_agent: &str) -> bool {
    let suspicious_patterns = [
        "bot", "crawler", "spider", "scraper",
        "curl", "wget", "python", "java",
        "scanner", "exploit", "hack"
    ];
    
    let ua_lower = user_agent.to_lowercase();
    suspicious_patterns.iter().any(|pattern| ua_lower.contains(pattern))
}

pub fn validate_csrf_token(req: &HttpRequest, expected_token: &str) -> bool {
    if let Some(token_header) = req.headers().get("X-CSRF-Token") {
        if let Ok(token_str) = token_header.to_str() {
            return token_str == expected_token;
        }
    }
    false
}

pub fn create_security_headers() -> HashMap<&'static str, &'static str> {
    let mut headers = HashMap::new();
    
    // OWASP Security Headers
    headers.insert("X-Content-Type-Options", "nosniff");
    headers.insert("X-Frame-Options", "DENY");
    headers.insert("X-XSS-Protection", "1; mode=block");
    headers.insert("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
    headers.insert("Content-Security-Policy", "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com; img-src 'self' data:; font-src 'self'");
    headers.insert("Referrer-Policy", "strict-origin-when-cross-origin");
    headers.insert("Permissions-Policy", "geolocation=(), microphone=(), camera=()");
    
    headers
}
