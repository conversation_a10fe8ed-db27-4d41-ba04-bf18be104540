use jsonwebtoken::{decode, encode, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>};
use serde::{Deserialize, Serialize};
use chrono::{Duration, Utc};
use uuid::Uuid;
use std::collections::HashMap;
use dashmap::DashMap;
use anyhow::{Result, anyhow};
use tracing::{info, warn, error};

/// Claims JWT avancés avec métadonnées de sécurité
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AdvancedClaims {
    pub sub: String,           // Subject (username)
    pub exp: usize,           // Expiration (24h)
    pub iat: usize,           // Issued at
    pub jti: String,          // JWT ID unique
    pub iss: String,          // Issuer
    pub aud: String,          // Audience
    pub role: String,         // User role
    pub session_id: String,   // Session ID
    pub ip_address: String,   // IP address
    pub user_agent: String,   // User agent
    pub permissions: Vec<String>, // User permissions
    pub last_activity: usize, // Last activity timestamp
}

/// Configuration JWT sécurisée
#[derive(Debug, <PERSON>lone)]
pub struct JwtConfig {
    pub secret: Vec<u8>,
    pub issuer: String,
    pub audience: String,
    pub expiration_hours: i64,
    pub algorithm: Algorithm,
}

impl Default for JwtConfig {
    fn default() -> Self {
        Self {
            secret: std::env::var("JWT_SECRET")
                .unwrap_or_else(|_| "ultra-secure-dcop413-jwt-secret-key-2025".to_string())
                .into_bytes(),
            issuer: "DCOP413-VisitorManagement".to_string(),
            audience: "DCOP413-Users".to_string(),
            expiration_hours: 24,
            algorithm: Algorithm::HS256,
        }
    }
}

/// Gestionnaire JWT avancé avec cache et révocation
pub struct AdvancedJwtManager {
    config: JwtConfig,
    active_tokens: DashMap<String, AdvancedClaims>,
    revoked_tokens: DashMap<String, usize>, // jti -> revocation_time
}

impl AdvancedJwtManager {
    pub fn new(config: JwtConfig) -> Self {
        Self {
            config,
            active_tokens: DashMap::new(),
            revoked_tokens: DashMap::new(),
        }
    }

    /// Créer un JWT avancé avec toutes les métadonnées de sécurité
    pub fn create_advanced_jwt(
        &self,
        username: &str,
        role: &str,
        permissions: Vec<String>,
        ip_address: &str,
        user_agent: &str,
    ) -> Result<String> {
        let now = Utc::now();
        let exp = now + Duration::hours(self.config.expiration_hours);
        let jti = Uuid::new_v4().to_string();
        let session_id = Uuid::new_v4().to_string();

        let claims = AdvancedClaims {
            sub: username.to_owned(),
            exp: exp.timestamp() as usize,
            iat: now.timestamp() as usize,
            jti: jti.clone(),
            iss: self.config.issuer.clone(),
            aud: self.config.audience.clone(),
            role: role.to_owned(),
            session_id,
            ip_address: ip_address.to_owned(),
            user_agent: user_agent.to_owned(),
            permissions,
            last_activity: now.timestamp() as usize,
        };

        let mut header = Header::new(self.config.algorithm);
        header.kid = Some("dcop413-key-1".to_string());

        let token = encode(
            &header,
            &claims,
            &EncodingKey::from_secret(&self.config.secret),
        )?;

        // Stocker le token actif
        self.active_tokens.insert(jti.clone(), claims.clone());

        info!(
            "JWT créé pour utilisateur: {}, role: {}, session: {}, IP: {}",
            username, role, claims.session_id, ip_address
        );

        Ok(token)
    }

    /// Valider un JWT avec vérifications de sécurité avancées
    pub fn validate_advanced_jwt(
        &self,
        token: &str,
        expected_ip: Option<&str>,
        expected_user_agent: Option<&str>,
    ) -> Result<AdvancedClaims> {
        // Décoder le token
        let mut validation = Validation::new(self.config.algorithm);
        validation.set_issuer(&[&self.config.issuer]);
        validation.set_audience(&[&self.config.audience]);

        let token_data = decode::<AdvancedClaims>(
            token,
            &DecodingKey::from_secret(&self.config.secret),
            &validation,
        )?;

        let claims = token_data.claims;

        // Vérifier si le token est révoqué
        if self.revoked_tokens.contains_key(&claims.jti) {
            warn!("Tentative d'utilisation d'un token révoqué: {}", claims.jti);
            return Err(anyhow!("Token révoqué"));
        }

        // Vérifier si le token est encore actif
        if !self.active_tokens.contains_key(&claims.jti) {
            warn!("Token non trouvé dans les tokens actifs: {}", claims.jti);
            return Err(anyhow!("Token non actif"));
        }

        // Vérifications de sécurité supplémentaires
        if let Some(expected_ip) = expected_ip {
            if claims.ip_address != expected_ip {
                error!(
                    "Adresse IP différente pour le token. Attendue: {}, Reçue: {}",
                    expected_ip, claims.ip_address
                );
                return Err(anyhow!("Adresse IP non autorisée"));
            }
        }

        if let Some(expected_ua) = expected_user_agent {
            if claims.user_agent != expected_ua {
                warn!(
                    "User-Agent différent pour le token. Attendu: {}, Reçu: {}",
                    expected_ua, claims.user_agent
                );
                // Note: On peut choisir de ne pas bloquer pour le User-Agent
            }
        }

        // Vérifier l'activité récente (pas plus de 30 minutes d'inactivité)
        let now = Utc::now().timestamp() as usize;
        if now - claims.last_activity > 1800 { // 30 minutes
            warn!("Token inactif depuis trop longtemps: {}", claims.jti);
            self.revoke_token(&claims.jti);
            return Err(anyhow!("Session expirée par inactivité"));
        }

        // Mettre à jour l'activité
        self.update_last_activity(&claims.jti)?;

        info!(
            "JWT validé avec succès pour utilisateur: {}, session: {}",
            claims.sub, claims.session_id
        );

        Ok(claims)
    }

    /// Mettre à jour la dernière activité d'un token
    pub fn update_last_activity(&self, jti: &str) -> Result<()> {
        if let Some(mut claims) = self.active_tokens.get_mut(jti) {
            claims.last_activity = Utc::now().timestamp() as usize;
            Ok(())
        } else {
            Err(anyhow!("Token non trouvé"))
        }
    }

    /// Révoquer un token
    pub fn revoke_token(&self, jti: &str) {
        let now = Utc::now().timestamp() as usize;
        self.revoked_tokens.insert(jti.to_string(), now);
        self.active_tokens.remove(jti);
        
        info!("Token révoqué: {}", jti);
    }

    /// Révoquer tous les tokens d'un utilisateur
    pub fn revoke_user_tokens(&self, username: &str) {
        let tokens_to_revoke: Vec<String> = self
            .active_tokens
            .iter()
            .filter(|entry| entry.value().sub == username)
            .map(|entry| entry.key().clone())
            .collect();

        for jti in tokens_to_revoke {
            self.revoke_token(&jti);
        }

        info!("Tous les tokens révoqués pour l'utilisateur: {}", username);
    }

    /// Nettoyer les tokens expirés
    pub fn cleanup_expired_tokens(&self) {
        let now = Utc::now().timestamp() as usize;
        
        // Nettoyer les tokens actifs expirés
        let expired_active: Vec<String> = self
            .active_tokens
            .iter()
            .filter(|entry| entry.value().exp < now)
            .map(|entry| entry.key().clone())
            .collect();

        for jti in expired_active {
            self.active_tokens.remove(&jti);
        }

        // Nettoyer les tokens révoqués anciens (plus de 7 jours)
        let week_ago = now - (7 * 24 * 3600);
        let old_revoked: Vec<String> = self
            .revoked_tokens
            .iter()
            .filter(|entry| *entry.value() < week_ago)
            .map(|entry| entry.key().clone())
            .collect();

        for jti in old_revoked {
            self.revoked_tokens.remove(&jti);
        }

        info!("Nettoyage des tokens expirés terminé");
    }

    /// Obtenir les statistiques des tokens
    pub fn get_token_stats(&self) -> HashMap<String, usize> {
        let mut stats = HashMap::new();
        stats.insert("active_tokens".to_string(), self.active_tokens.len());
        stats.insert("revoked_tokens".to_string(), self.revoked_tokens.len());
        stats
    }
}

// Instance globale du gestionnaire JWT
use std::sync::OnceLock;
static JWT_MANAGER: OnceLock<AdvancedJwtManager> = OnceLock::new();

pub fn get_jwt_manager() -> &'static AdvancedJwtManager {
    JWT_MANAGER.get_or_init(|| {
        let config = JwtConfig::default();
        AdvancedJwtManager::new(config)
    })
}

/// Créer un JWT avancé
pub fn create_advanced_jwt(
    username: &str,
    role: &str,
    permissions: Vec<String>,
    ip_address: &str,
    user_agent: &str,
) -> Result<String> {
    get_jwt_manager().create_advanced_jwt(username, role, permissions, ip_address, user_agent)
}

/// Valider un JWT avancé
pub fn validate_advanced_jwt(
    token: &str,
    expected_ip: Option<&str>,
    expected_user_agent: Option<&str>,
) -> Result<AdvancedClaims> {
    get_jwt_manager().validate_advanced_jwt(token, expected_ip, expected_user_agent)
}

/// Révoquer un token
pub fn revoke_token(jti: &str) {
    get_jwt_manager().revoke_token(jti);
}

/// Révoquer tous les tokens d'un utilisateur
pub fn revoke_user_tokens(username: &str) {
    get_jwt_manager().revoke_user_tokens(username);
}

/// Nettoyer les tokens expirés
pub fn cleanup_expired_tokens() {
    get_jwt_manager().cleanup_expired_tokens();
}

/// Obtenir les statistiques des tokens
pub fn get_token_stats() -> HashMap<String, usize> {
    get_jwt_manager().get_token_stats()
}
