use regex::Regex;
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use anyhow::{Result, anyhow};
use tracing::{warn, error, info};

/// Résultat de validation avancée
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ValidationResult {
    pub is_valid: bool,
    pub field: String,
    pub value: String,
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
    pub security_level: SecurityLevel,
    pub sanitized_value: Option<String>,
}

/// Niveau de sécurité détecté
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub enum SecurityLevel {
    Safe,       // Sûr
    Suspicious, // Suspect
    Dangerous,  // Dangereux
    Blocked,    // Bloqué
}

/// Patterns malveillants détectés
#[derive(Debug, Clone)]
pub struct MaliciousPattern {
    pub name: String,
    pub regex: Regex,
    pub severity: SecurityLevel,
    pub description: String,
}

/// Gestionnaire de validation avancée
pub struct AdvancedValidator {
    malicious_patterns: Vec<MaliciousPattern>,
    field_validators: HashMap<String, Vec<Regex>>,
    sanitization_rules: HashMap<String, Vec<(Regex, String)>>,
}

impl AdvancedValidator {
    pub fn new() -> Self {
        let mut validator = Self {
            malicious_patterns: Vec::new(),
            field_validators: HashMap::new(),
            sanitization_rules: HashMap::new(),
        };
        
        validator.initialize_malicious_patterns();
        validator.initialize_field_validators();
        validator.initialize_sanitization_rules();
        
        validator
    }

    /// Initialiser les patterns malveillants
    fn initialize_malicious_patterns(&mut self) {
        let patterns = vec![
            // XSS Patterns
            ("XSS_SCRIPT", r"<\s*script[^>]*>.*?</\s*script\s*>", SecurityLevel::Blocked, "Script tag détecté"),
            ("XSS_JAVASCRIPT", r"javascript\s*:", SecurityLevel::Blocked, "JavaScript URL détecté"),
            ("XSS_ONERROR", r"on\w+\s*=", SecurityLevel::Dangerous, "Event handler détecté"),
            ("XSS_IFRAME", r"<\s*iframe[^>]*>", SecurityLevel::Dangerous, "Iframe détecté"),
            
            // SQL Injection Patterns
            ("SQL_UNION", r"(?i)\bunion\b.*\bselect\b", SecurityLevel::Blocked, "SQL Union détecté"),
            ("SQL_DROP", r"(?i)\bdrop\b.*\btable\b", SecurityLevel::Blocked, "SQL Drop détecté"),
            ("SQL_DELETE", r"(?i)\bdelete\b.*\bfrom\b", SecurityLevel::Dangerous, "SQL Delete détecté"),
            ("SQL_INSERT", r"(?i)\binsert\b.*\binto\b", SecurityLevel::Dangerous, "SQL Insert détecté"),
            ("SQL_UPDATE", r"(?i)\bupdate\b.*\bset\b", SecurityLevel::Dangerous, "SQL Update détecté"),
            ("SQL_COMMENT", r"--|\#|/\*|\*/", SecurityLevel::Suspicious, "Commentaire SQL détecté"),
            
            // Command Injection
            ("CMD_PIPE", r"[|&;`]", SecurityLevel::Dangerous, "Caractère de commande détecté"),
            ("CMD_REDIRECT", r"[<>]", SecurityLevel::Suspicious, "Redirection détectée"),
            
            // Path Traversal
            ("PATH_TRAVERSAL", r"\.\./", SecurityLevel::Dangerous, "Path traversal détecté"),
            ("PATH_ABSOLUTE", r"^/[a-zA-Z]", SecurityLevel::Suspicious, "Chemin absolu détecté"),
            
            // LDAP Injection
            ("LDAP_INJECTION", r"[()=*!&|]", SecurityLevel::Suspicious, "Caractère LDAP suspect"),
            
            // NoSQL Injection
            ("NOSQL_INJECTION", r"\$\w+", SecurityLevel::Suspicious, "Opérateur NoSQL détecté"),
            
            // File Upload
            ("DANGEROUS_EXTENSION", r"\.(exe|bat|cmd|com|pif|scr|vbs|js|jar|php|asp|jsp)$", SecurityLevel::Blocked, "Extension dangereuse"),
            
            // Encoding Attacks
            ("URL_ENCODING", r"%[0-9a-fA-F]{2}", SecurityLevel::Suspicious, "URL encoding détecté"),
            ("UNICODE_ENCODING", r"\\u[0-9a-fA-F]{4}", SecurityLevel::Suspicious, "Unicode encoding détecté"),
        ];

        for (name, pattern, severity, description) in patterns {
            if let Ok(regex) = Regex::new(pattern) {
                self.malicious_patterns.push(MaliciousPattern {
                    name: name.to_string(),
                    regex,
                    severity,
                    description: description.to_string(),
                });
            }
        }

        info!("Patterns malveillants initialisés: {}", self.malicious_patterns.len());
    }

    /// Initialiser les validateurs par champ
    fn initialize_field_validators(&mut self) {
        // Email
        if let Ok(email_regex) = Regex::new(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$") {
            self.field_validators.insert("email".to_string(), vec![email_regex]);
        }

        // Téléphone (format international)
        if let Ok(phone_regex) = Regex::new(r"^\+?[1-9]\d{1,14}$") {
            self.field_validators.insert("telephone".to_string(), vec![phone_regex]);
        }

        // Nom/Prénom (lettres, espaces, tirets, apostrophes)
        if let Ok(name_regex) = Regex::new(r"^[a-zA-ZÀ-ÿ\s\-']{2,50}$") {
            self.field_validators.insert("nom".to_string(), vec![name_regex.clone()]);
            self.field_validators.insert("prenom".to_string(), vec![name_regex.clone()]);
            self.field_validators.insert("postnom".to_string(), vec![name_regex]);
        }

        // Numéro de pièce d'identité
        if let Ok(piece_regex) = Regex::new(r"^[A-Z0-9\-]{5,20}$") {
            self.field_validators.insert("numero_piece".to_string(), vec![piece_regex]);
        }

        // Organisation
        if let Ok(org_regex) = Regex::new(r"^[a-zA-ZÀ-ÿ0-9\s\-'.,&()]{2,100}$") {
            self.field_validators.insert("organisation".to_string(), vec![org_regex]);
        }
    }

    /// Initialiser les règles de sanitisation
    fn initialize_sanitization_rules(&mut self) {
        let mut html_rules = Vec::new();
        
        // Supprimer les tags HTML
        if let Ok(html_tag) = Regex::new(r"<[^>]*>") {
            html_rules.push((html_tag, "".to_string()));
        }
        
        // Échapper les caractères spéciaux
        if let Ok(ampersand) = Regex::new(r"&") {
            html_rules.push((ampersand, "&amp;".to_string()));
        }
        
        if let Ok(less_than) = Regex::new(r"<") {
            html_rules.push((less_than, "&lt;".to_string()));
        }
        
        if let Ok(greater_than) = Regex::new(r">") {
            html_rules.push((greater_than, "&gt;".to_string()));
        }
        
        if let Ok(quote) = Regex::new(r#"""#) {
            html_rules.push((quote, "&quot;".to_string()));
        }
        
        if let Ok(apostrophe) = Regex::new(r"'") {
            html_rules.push((apostrophe, "&#x27;".to_string()));
        }

        self.sanitization_rules.insert("html".to_string(), html_rules);
    }

    /// Valider une valeur avec détection de patterns malveillants
    pub fn validate_field(&self, field: &str, value: &str) -> ValidationResult {
        let mut result = ValidationResult {
            is_valid: true,
            field: field.to_string(),
            value: value.to_string(),
            errors: Vec::new(),
            warnings: Vec::new(),
            security_level: SecurityLevel::Safe,
            sanitized_value: None,
        };

        // Vérifier les patterns malveillants
        for pattern in &self.malicious_patterns {
            if pattern.regex.is_match(value) {
                match pattern.severity {
                    SecurityLevel::Blocked => {
                        result.is_valid = false;
                        result.errors.push(format!("Contenu bloqué: {}", pattern.description));
                        result.security_level = SecurityLevel::Blocked;
                        error!("Pattern malveillant bloqué: {} dans le champ {}", pattern.name, field);
                    },
                    SecurityLevel::Dangerous => {
                        result.warnings.push(format!("Contenu dangereux: {}", pattern.description));
                        if result.security_level == SecurityLevel::Safe {
                            result.security_level = SecurityLevel::Dangerous;
                        }
                        warn!("Pattern dangereux détecté: {} dans le champ {}", pattern.name, field);
                    },
                    SecurityLevel::Suspicious => {
                        result.warnings.push(format!("Contenu suspect: {}", pattern.description));
                        if result.security_level == SecurityLevel::Safe {
                            result.security_level = SecurityLevel::Suspicious;
                        }
                        warn!("Pattern suspect détecté: {} dans le champ {}", pattern.name, field);
                    },
                    SecurityLevel::Safe => {},
                }
            }
        }

        // Validation spécifique au champ
        if let Some(validators) = self.field_validators.get(field) {
            for validator in validators {
                if !validator.is_match(value) {
                    result.is_valid = false;
                    result.errors.push(format!("Format invalide pour le champ {}", field));
                }
            }
        }

        // Sanitisation
        result.sanitized_value = Some(self.sanitize_value(value, "html"));

        result
    }

    /// Sanitiser une valeur
    pub fn sanitize_value(&self, value: &str, rule_set: &str) -> String {
        if let Some(rules) = self.sanitization_rules.get(rule_set) {
            let mut sanitized = value.to_string();
            for (regex, replacement) in rules {
                sanitized = regex.replace_all(&sanitized, replacement).to_string();
            }
            sanitized
        } else {
            value.to_string()
        }
    }

    /// Valider un formulaire complet
    pub fn validate_form(&self, form_data: &HashMap<String, String>) -> HashMap<String, ValidationResult> {
        let mut results = HashMap::new();
        
        for (field, value) in form_data {
            let result = self.validate_field(field, value);
            results.insert(field.clone(), result);
        }
        
        results
    }

    /// Vérifier si un formulaire est globalement valide
    pub fn is_form_valid(&self, validation_results: &HashMap<String, ValidationResult>) -> bool {
        validation_results.values().all(|result| result.is_valid)
    }

    /// Obtenir les statistiques de validation
    pub fn get_validation_stats(&self) -> HashMap<String, usize> {
        let mut stats = HashMap::new();
        stats.insert("malicious_patterns".to_string(), self.malicious_patterns.len());
        stats.insert("field_validators".to_string(), self.field_validators.len());
        stats.insert("sanitization_rules".to_string(), self.sanitization_rules.len());
        stats
    }
}

// Instance globale du validateur
use std::sync::OnceLock;
static VALIDATOR: OnceLock<AdvancedValidator> = OnceLock::new();

pub fn get_validator() -> &'static AdvancedValidator {
    VALIDATOR.get_or_init(|| AdvancedValidator::new())
}

/// Valider un champ
pub fn validate_field(field: &str, value: &str) -> ValidationResult {
    get_validator().validate_field(field, value)
}

/// Valider un formulaire
pub fn validate_form(form_data: &HashMap<String, String>) -> HashMap<String, ValidationResult> {
    get_validator().validate_form(form_data)
}

/// Sanitiser une valeur
pub fn sanitize_value(value: &str, rule_set: &str) -> String {
    get_validator().sanitize_value(value, rule_set)
}

/// Vérifier si un formulaire est valide
pub fn is_form_valid(validation_results: &HashMap<String, ValidationResult>) -> bool {
    get_validator().is_form_valid(validation_results)
}

/// Obtenir les statistiques de validation
pub fn get_validation_stats() -> HashMap<String, usize> {
    get_validator().get_validation_stats()
}
