use aes_gcm::{
    aead::{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Key<PERSON>ni<PERSON>, OsRng},
    Aes256Gcm, Key, Nonce,
};
use base64::{Engine as _, engine::general_purpose};
use anyhow::{Result, anyhow};
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use tracing::{info, warn, error};
use chrono::{DateTime, Utc};

/// Structure pour les données chiffrées
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct EncryptedData {
    pub data: String,      // Données chiffrées en base64
    pub nonce: String,     // Nonce en base64
    pub key_id: String,    // ID de la clé utilisée
    pub timestamp: DateTime<Utc>, // Timestamp du chiffrement
}

/// Configuration du chiffrement
#[derive(Debug, Clone)]
pub struct EncryptionConfig {
    pub master_key: Vec<u8>,
    pub key_rotation_days: i64,
    pub algorithm: String,
}

impl Default for EncryptionConfig {
    fn default() -> Self {
        Self {
            master_key: std::env::var("ENCRYPTION_MASTER_KEY")
                .unwrap_or_else(|_| "ultra-secure-dcop413-master-encryption-key-256bits-2025".to_string())
                .into_bytes(),
            key_rotation_days: 30,
            algorithm: "AES-256-GCM".to_string(),
        }
    }
}

/// Gestionnaire de chiffrement AES-256-GCM
pub struct EncryptionManager {
    config: EncryptionConfig,
    active_keys: HashMap<String, Vec<u8>>,
    current_key_id: String,
}

impl EncryptionManager {
    pub fn new(config: EncryptionConfig) -> Result<Self> {
        let mut manager = Self {
            config,
            active_keys: HashMap::new(),
            current_key_id: String::new(),
        };
        
        // Générer la clé initiale
        manager.generate_new_key()?;
        
        Ok(manager)
    }

    /// Générer une nouvelle clé de chiffrement
    pub fn generate_new_key(&mut self) -> Result<String> {
        let key_id = format!("dcop413-key-{}", Utc::now().timestamp());
        
        // Dériver une clé à partir de la clé maître
        let key = self.derive_key(&self.config.master_key, &key_id)?;
        
        self.active_keys.insert(key_id.clone(), key);
        self.current_key_id = key_id.clone();
        
        info!("Nouvelle clé de chiffrement générée: {}", key_id);
        
        Ok(key_id)
    }

    /// Dériver une clé à partir de la clé maître
    fn derive_key(&self, master_key: &[u8], key_id: &str) -> Result<Vec<u8>> {
        use sha2::{Sha256, Digest};
        
        let mut hasher = Sha256::new();
        hasher.update(master_key);
        hasher.update(key_id.as_bytes());
        hasher.update(b"DCOP413-ENCRYPTION");
        
        let result = hasher.finalize();
        Ok(result.to_vec())
    }

    /// Chiffrer des données sensibles
    pub fn encrypt_sensitive_data(&self, plaintext: &str) -> Result<EncryptedData> {
        let key_bytes = self.active_keys.get(&self.current_key_id)
            .ok_or_else(|| anyhow!("Clé de chiffrement non trouvée"))?;

        let key = Key::<Aes256Gcm>::from_slice(key_bytes);
        let cipher = Aes256Gcm::new(key);
        
        // Générer un nonce aléatoire
        let nonce = Aes256Gcm::generate_nonce(&mut OsRng);
        
        // Chiffrer les données
        let ciphertext = cipher.encrypt(&nonce, plaintext.as_bytes())
            .map_err(|e| anyhow!("Erreur de chiffrement: {}", e))?;

        let encrypted_data = EncryptedData {
            data: general_purpose::STANDARD.encode(&ciphertext),
            nonce: general_purpose::STANDARD.encode(&nonce),
            key_id: self.current_key_id.clone(),
            timestamp: Utc::now(),
        };

        info!("Données chiffrées avec la clé: {}", self.current_key_id);
        
        Ok(encrypted_data)
    }

    /// Déchiffrer des données sensibles
    pub fn decrypt_sensitive_data(&self, encrypted_data: &EncryptedData) -> Result<String> {
        let key_bytes = self.active_keys.get(&encrypted_data.key_id)
            .ok_or_else(|| anyhow!("Clé de déchiffrement non trouvée: {}", encrypted_data.key_id))?;

        let key = Key::<Aes256Gcm>::from_slice(key_bytes);
        let cipher = Aes256Gcm::new(key);
        
        // Décoder les données
        let ciphertext = general_purpose::STANDARD.decode(&encrypted_data.data)
            .map_err(|e| anyhow!("Erreur de décodage des données: {}", e))?;
        
        let nonce_bytes = general_purpose::STANDARD.decode(&encrypted_data.nonce)
            .map_err(|e| anyhow!("Erreur de décodage du nonce: {}", e))?;
        
        let nonce = Nonce::from_slice(&nonce_bytes);
        
        // Déchiffrer les données
        let plaintext = cipher.decrypt(nonce, ciphertext.as_ref())
            .map_err(|e| anyhow!("Erreur de déchiffrement: {}", e))?;

        let result = String::from_utf8(plaintext)
            .map_err(|e| anyhow!("Erreur de conversion UTF-8: {}", e))?;

        info!("Données déchiffrées avec la clé: {}", encrypted_data.key_id);
        
        Ok(result)
    }

    /// Chiffrer des données personnelles (nom, prénom, etc.)
    pub fn encrypt_personal_data(&self, data: &HashMap<String, String>) -> Result<HashMap<String, EncryptedData>> {
        let mut encrypted_map = HashMap::new();
        
        for (field, value) in data {
            if self.is_sensitive_field(field) {
                let encrypted = self.encrypt_sensitive_data(value)?;
                encrypted_map.insert(field.clone(), encrypted);
            }
        }
        
        Ok(encrypted_map)
    }

    /// Déchiffrer des données personnelles
    pub fn decrypt_personal_data(&self, encrypted_data: &HashMap<String, EncryptedData>) -> Result<HashMap<String, String>> {
        let mut decrypted_map = HashMap::new();
        
        for (field, encrypted) in encrypted_data {
            let decrypted = self.decrypt_sensitive_data(encrypted)?;
            decrypted_map.insert(field.clone(), decrypted);
        }
        
        Ok(decrypted_map)
    }

    /// Vérifier si un champ est sensible
    fn is_sensitive_field(&self, field: &str) -> bool {
        matches!(field, 
            "nom" | "prenom" | "postnom" | 
            "telephone1" | "telephone2" | "email" |
            "numero_piece" | "adresse_complete" |
            "notes_securite" | "observations"
        )
    }

    /// Rotation des clés de chiffrement
    pub fn rotate_keys_if_needed(&mut self) -> Result<bool> {
        let current_key_timestamp = self.current_key_id
            .split('-')
            .last()
            .and_then(|s| s.parse::<i64>().ok())
            .unwrap_or(0);

        let now = Utc::now().timestamp();
        let rotation_interval = self.config.key_rotation_days * 24 * 3600;

        if now - current_key_timestamp > rotation_interval {
            self.generate_new_key()?;
            info!("Rotation des clés effectuée");
            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// Nettoyer les anciennes clés
    pub fn cleanup_old_keys(&mut self) {
        let cutoff = Utc::now().timestamp() - (90 * 24 * 3600); // 90 jours
        
        let old_keys: Vec<String> = self.active_keys
            .keys()
            .filter(|key_id| {
                key_id.split('-')
                    .last()
                    .and_then(|s| s.parse::<i64>().ok())
                    .map(|timestamp| timestamp < cutoff)
                    .unwrap_or(false)
            })
            .cloned()
            .collect();

        for key_id in old_keys {
            if key_id != self.current_key_id {
                self.active_keys.remove(&key_id);
                info!("Ancienne clé supprimée: {}", key_id);
            }
        }
    }

    /// Obtenir les statistiques de chiffrement
    pub fn get_encryption_stats(&self) -> HashMap<String, serde_json::Value> {
        let mut stats = HashMap::new();
        stats.insert("active_keys".to_string(), serde_json::Value::Number(self.active_keys.len().into()));
        stats.insert("current_key_id".to_string(), serde_json::Value::String(self.current_key_id.clone()));
        stats.insert("algorithm".to_string(), serde_json::Value::String(self.config.algorithm.clone()));
        stats
    }
}

// Instance globale du gestionnaire de chiffrement
use std::sync::{OnceLock, Mutex};
static ENCRYPTION_MANAGER: OnceLock<Mutex<EncryptionManager>> = OnceLock::new();

pub fn get_encryption_manager() -> &'static Mutex<EncryptionManager> {
    ENCRYPTION_MANAGER.get_or_init(|| {
        let config = EncryptionConfig::default();
        let manager = EncryptionManager::new(config)
            .expect("Impossible d'initialiser le gestionnaire de chiffrement");
        Mutex::new(manager)
    })
}

/// Chiffrer des données sensibles
pub fn encrypt_data(plaintext: &str) -> Result<EncryptedData> {
    let manager = get_encryption_manager().lock()
        .map_err(|e| anyhow!("Erreur de verrouillage: {}", e))?;
    manager.encrypt_sensitive_data(plaintext)
}

/// Déchiffrer des données sensibles
pub fn decrypt_data(encrypted_data: &EncryptedData) -> Result<String> {
    let manager = get_encryption_manager().lock()
        .map_err(|e| anyhow!("Erreur de verrouillage: {}", e))?;
    manager.decrypt_sensitive_data(encrypted_data)
}

/// Chiffrer des données personnelles
pub fn encrypt_personal_data(data: &HashMap<String, String>) -> Result<HashMap<String, EncryptedData>> {
    let manager = get_encryption_manager().lock()
        .map_err(|e| anyhow!("Erreur de verrouillage: {}", e))?;
    manager.encrypt_personal_data(data)
}

/// Déchiffrer des données personnelles
pub fn decrypt_personal_data(encrypted_data: &HashMap<String, EncryptedData>) -> Result<HashMap<String, String>> {
    let manager = get_encryption_manager().lock()
        .map_err(|e| anyhow!("Erreur de verrouillage: {}", e))?;
    manager.decrypt_personal_data(encrypted_data)
}

/// Effectuer la rotation des clés si nécessaire
pub fn rotate_keys_if_needed() -> Result<bool> {
    let mut manager = get_encryption_manager().lock()
        .map_err(|e| anyhow!("Erreur de verrouillage: {}", e))?;
    manager.rotate_keys_if_needed()
}

/// Nettoyer les anciennes clés
pub fn cleanup_old_keys() {
    if let Ok(mut manager) = get_encryption_manager().lock() {
        manager.cleanup_old_keys();
    }
}

/// Obtenir les statistiques de chiffrement
pub fn get_encryption_stats() -> HashMap<String, serde_json::Value> {
    get_encryption_manager().lock()
        .map(|manager| manager.get_encryption_stats())
        .unwrap_or_default()
}
