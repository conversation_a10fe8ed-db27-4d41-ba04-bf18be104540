-- =====================================================
-- Script de configuration PostgreSQL pour DCOP 413
-- Création de la base de données et des utilisateurs
-- =====================================================

-- Créer la base de données principale
CREATE DATABASE app_gestion_visiteurs
    WITH 
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'fr_FR.UTF-8'
    LC_CTYPE = 'fr_FR.UTF-8'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1
    IS_TEMPLATE = False;

-- Se connecter à la nouvelle base de données
\c app_gestion_visiteurs;

-- Créer les extensions nécessaires
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Créer l'utilisateur pour l'application
CREATE USER app_user WITH
    LOGIN
    NOSUPERUSER
    NOCREATEDB
    NOCREATEROLE
    INHERIT
    NOREPLICATION
    CONNECTION LIMIT -1
    PASSWORD 'SecureAppPassword2025!';

-- Créer l'utilisateur administrateur
CREATE USER app_admin WITH
    LOGIN
    NOSUPERUSER
    CREATEDB
    CREATEROLE
    INHERIT
    NOREPLICATION
    CONNECTION LIMIT -1
    PASSWORD 'SecureAdminPassword2025!';

-- Accorder les privilèges sur la base de données
GRANT CONNECT ON DATABASE app_gestion_visiteurs TO app_user;
GRANT CONNECT ON DATABASE app_gestion_visiteurs TO app_admin;

-- Accorder les privilèges sur le schéma public
GRANT USAGE ON SCHEMA public TO app_user;
GRANT CREATE ON SCHEMA public TO app_user;
GRANT USAGE ON SCHEMA public TO app_admin;
GRANT CREATE ON SCHEMA public TO app_admin;

-- Accorder les privilèges sur les séquences
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO app_user;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO app_admin;

-- Accorder les privilèges par défaut pour les futures tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO app_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT USAGE, SELECT ON SEQUENCES TO app_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO app_admin;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO app_admin;

-- Configuration de sécurité
ALTER SYSTEM SET password_encryption = 'scram-sha-256';
ALTER SYSTEM SET ssl = on;
ALTER SYSTEM SET log_connections = on;
ALTER SYSTEM SET log_disconnections = on;
ALTER SYSTEM SET log_statement = 'mod';
ALTER SYSTEM SET log_min_duration_statement = '1000';

-- Recharger la configuration
SELECT pg_reload_conf();

-- Afficher les informations de connexion
\echo '=============================================='
\echo 'Configuration PostgreSQL terminée !'
\echo '=============================================='
\echo 'Base de données: app_gestion_visiteurs'
\echo 'Utilisateur app: app_user'
\echo 'Utilisateur admin: app_admin'
\echo '=============================================='
\echo 'URLs de connexion:'
\echo 'App: postgresql://app_user:SecureAppPassword2025!@localhost:5432/app_gestion_visiteurs'
\echo 'Admin: postgresql://app_admin:SecureAdminPassword2025!@localhost:5432/app_gestion_visiteurs'
\echo '=============================================='
