/* Fichier CSS consolidé - professional-design.css */
/* Fusion des styles de professional-design.css, clean.css et photo-styles.css */
/* DCOP 413 - Gestion des Visiteurs */

/* Variables CSS communes */
:root {
  /* Couleurs principales */
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --primary-active: #1e40af;
  --primary-light: #dbeafe;
  --primary-dark: #1e3a8a;

  /* Couleurs secondaires */
  --success-color: #059669;
  --success-hover: #047857;
  --success-light: #d1fae5;
  --warning-color: #d97706;
  --warning-light: #fef3c7;
  --danger-color: #dc2626;
  --danger-hover: #b91c1c;
  --danger-light: #fee2e2;

  /* Nuances de gris */
  --gray-25: #fcfcfd;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Transitions et animations */
  --transition-fast: all 0.15s ease-out;
  --transition-normal: all 0.2s ease-in-out;
  --transition-slow: all 0.3s ease-in-out;
  --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Ombres */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* Bordures */
  --border-radius-xs: 0.125rem;
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 0.75rem;
  --border-radius-2xl: 1rem;
  --border-radius-3xl: 1.5rem;
  --border-radius-full: 9999px;

  /* Espacements */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
}

/* ===== STYLES DE BASE ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.6;
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: var(--gray-50);
  color: var(--gray-800);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== HEADER ULTRA-MODERNE ===== */
.ultra-modern-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
  padding: var(--spacing-xl) 0 var(--spacing-lg) 0;
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
  min-height: 140px;
}

.ultra-modern-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

.header-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--spacing-xl);
  position: relative;
  z-index: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 120px;
}

.header-breadcrumb {
  display: none; /* Masquer le breadcrumb pour cette disposition */
}

.breadcrumb-item {
  color: rgba(255, 255, 255, 0.8);
}

.breadcrumb-item.active {
  color: white;
  font-weight: 500;
}

.breadcrumb-separator {
  color: rgba(255, 255, 255, 0.6);
}

/* Ligne du haut avec logo et photo */
.header-top-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  height: 60px;
  margin-bottom: var(--spacing-lg);
}

.header-logo-section {
  display: flex;
  align-items: center;
  height: 100%;
}

.header-logo {
  width: 70px;
  height: 70px;
  object-fit: contain;
  filter: brightness(1.1) contrast(1.1);
  transition: var(--transition-normal);
}

.header-logo:hover {
  transform: scale(1.05);
}

/* Ligne principale avec titre centré et navigation à droite */
.header-main-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  position: relative;
  height: 50px;
}

.header-center-section {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  text-align: center;
}

.header-main-title {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: white;
  letter-spacing: -0.025em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-right-section {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
}

/* ===== NAVIGATION HEADER ===== */
.header-nav-buttons {
  display: flex;
  gap: var(--spacing-lg);
  align-items: center;
  flex-wrap: nowrap;
}

.header-nav-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  background: transparent;
  color: white;
  text-decoration: none;
  border-radius: var(--border-radius-md);
  font-size: 0.9rem;
  font-weight: 500;
  transition: var(--transition-normal);
  border: none;
  position: relative;
  white-space: nowrap;
}

.header-nav-btn::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background: white;
  transition: width 0.3s ease;
}

.header-nav-btn:hover::after {
  width: 100%;
}

.header-nav-btn:hover {
  color: rgba(255, 255, 255, 0.9);
}

.header-nav-btn.deconnexion {
  background: rgba(220, 38, 38, 0.2);
  border: 1px solid rgba(220, 38, 38, 0.4);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm) var(--spacing-lg);
  margin-left: var(--spacing-md);
}

.header-nav-btn.deconnexion:hover {
  background: rgba(220, 38, 38, 0.35);
}

.header-nav-btn.deconnexion::after {
  display: none;
}

/* ===== PHOTO VISITEUR DANS HEADER ===== */
.header-visitor-photo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  height: 100%;
  position: relative;
}

.visitor-photo-container {
  width: 100px;
  height: 100px;
  border-radius: var(--border-radius-xl);
  overflow: hidden;
  border: 3px solid rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-normal);
  backdrop-filter: blur(10px);
  position: relative;
  cursor: pointer;
}

.visitor-photo-container:hover {
  border-color: rgba(255, 255, 255, 0.7);
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.visitor-photo-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.7rem;
  text-align: center;
  padding: var(--spacing-xs);
}

.photo-icon {
  font-size: 1.5rem;
  margin-bottom: 2px;
}

.photo-text {
  font-size: 0.65rem;
  font-weight: 600;
  line-height: 1.2;
}

.photo-hint {
  font-size: 0.55rem;
  opacity: 0.8;
  font-style: italic;
}

.visitor-photo-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* ===== OVERLAY PHOTO ===== */
.photo-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: var(--border-radius-xl);
}

.visitor-photo-container:hover .photo-overlay {
  opacity: 1;
}

.photo-overlay-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.photo-overlay-btn {
  width: 28px;
  height: 28px;
  border-radius: var(--border-radius-full);
  border: none;
  background: rgba(255, 255, 255, 0.9);
  color: var(--gray-700);
  font-size: 0.8rem;
  cursor: pointer;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
}

.photo-overlay-btn:hover {
  background: white;
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.photo-overlay-btn-remove {
  background: rgba(220, 38, 38, 0.9);
  color: white;
}

.photo-overlay-btn-remove:hover {
  background: var(--danger-color);
}

/* ===== BOUTONS D'ACTION PHOTO ===== */
.header-photo-actions {
  display: flex;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-xs);
  flex-wrap: wrap;
  justify-content: center;
}

.header-photo-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-md);
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.15);
  color: white;
  font-size: 0.6rem;
  cursor: pointer;
  transition: var(--transition-normal);
  backdrop-filter: blur(5px);
  min-width: 45px;
}

.header-photo-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.header-photo-btn-primary {
  background: rgba(34, 197, 94, 0.2);
  border-color: rgba(34, 197, 94, 0.4);
}

.header-photo-btn-primary:hover {
  background: rgba(34, 197, 94, 0.3);
  border-color: rgba(34, 197, 94, 0.6);
}

.header-photo-btn-secondary {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.4);
}

.header-photo-btn-secondary:hover {
  background: rgba(59, 130, 246, 0.3);
  border-color: rgba(59, 130, 246, 0.6);
}

.header-photo-btn-remove {
  background: rgba(220, 38, 38, 0.2);
  border-color: rgba(220, 38, 38, 0.4);
}

.header-photo-btn-remove:hover {
  background: rgba(220, 38, 38, 0.3);
  border-color: rgba(220, 38, 38, 0.6);
}

.btn-icon {
  font-size: 0.9rem;
}

.btn-text {
  font-size: 0.55rem;
  font-weight: 500;
  line-height: 1;
}

/* ===== INFORMATIONS PHOTO ===== */
.photo-info {
  display: flex;
  gap: var(--spacing-xs);
  margin-top: 2px;
  font-size: 0.5rem;
  color: rgba(255, 255, 255, 0.7);
}

.photo-size,
.photo-format {
  background: rgba(255, 255, 255, 0.1);
  padding: 1px 4px;
  border-radius: var(--border-radius-sm);
  font-weight: 500;
}

/* ===== DRAG & DROP STYLES ===== */
.visitor-photo-container.drag-over {
  border-color: var(--primary-color) !important;
  background: rgba(37, 99, 235, 0.2) !important;
  transform: scale(1.05);
  box-shadow: 0 0 20px rgba(37, 99, 235, 0.4);
}

.visitor-photo-container.drag-over .visitor-photo-placeholder {
  color: var(--primary-color);
}

.visitor-photo-container.drag-over .photo-icon {
  animation: bounce 0.6s ease-in-out infinite alternate;
}

/* ===== ANIMATIONS PHOTO ===== */
@keyframes bounce {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(-5px);
  }
}

@keyframes photoSuccess {
  0% {
    transform: scale(1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  }
}

.visitor-photo-container.photo-success {
  animation: photoSuccess 0.6s ease-in-out;
  border-color: var(--success-color) !important;
}

/* ===== ÉTATS INTERACTIFS ===== */
.visitor-photo-container:active {
  transform: scale(0.98);
}

.header-photo-btn:active {
  transform: translateY(0) scale(0.95);
}

.photo-overlay-btn:active {
  transform: scale(0.9);
}

/* ===== RESPONSIVE PHOTO ===== */
@media (max-width: 992px) {
  .visitor-photo-container {
    width: 80px;
    height: 80px;
  }

  .photo-icon {
    font-size: 1.2rem;
  }

  .photo-text {
    font-size: 0.6rem;
  }

  .photo-hint {
    font-size: 0.5rem;
  }

  .header-photo-btn {
    min-width: 40px;
    padding: 4px;
  }

  .btn-icon {
    font-size: 0.8rem;
  }

  .btn-text {
    font-size: 0.5rem;
  }
}

@media (max-width: 768px) {
  .visitor-photo-container {
    width: 70px;
    height: 70px;
  }

  .photo-overlay-btn {
    width: 24px;
    height: 24px;
    font-size: 0.7rem;
  }

  .header-photo-actions {
    gap: 2px;
  }

  .header-photo-btn {
    min-width: 35px;
    padding: 3px;
  }
}

@media (max-width: 480px) {
  .visitor-photo-container {
    width: 60px;
    height: 60px;
  }

  .photo-icon {
    font-size: 1rem;
  }

  .photo-text {
    font-size: 0.55rem;
  }

  .photo-hint {
    display: none;
  }

  .photo-overlay-btn {
    width: 20px;
    height: 20px;
    font-size: 0.6rem;
  }

  .header-photo-btn {
    min-width: 30px;
    padding: 2px;
  }

  .btn-text {
    font-size: 0.45rem;
  }
}

/* ===== SECTION CAMÉRA DANS HEADER ===== */
.header-camera-section {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-2xl);
  z-index: 1000;
  max-width: 500px;
  width: 90%;
}

.header-camera-video {
  width: 100%;
  height: auto;
  border-radius: var(--border-radius-md);
  background: var(--gray-100);
  margin-bottom: var(--spacing-md);
}

.header-camera-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
}

.header-camera-actions .header-photo-btn {
  width: auto;
  height: auto;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.875rem;
  border-radius: var(--border-radius-md);
  background: var(--primary-color);
  color: white;
}

.header-camera-actions .header-photo-btn:hover {
  background: var(--primary-hover);
}

/* ===== RESPONSIVE HEADER ===== */
@media (max-width: 1400px) {
  .header-nav-buttons {
    gap: var(--spacing-md);
  }

  .header-nav-btn {
    font-size: 0.85rem;
    padding: var(--spacing-sm) var(--spacing-md);
  }
}

@media (max-width: 1200px) {
  .header-container {
    padding: 0 var(--spacing-lg);
  }

  .header-nav-buttons {
    gap: var(--spacing-sm);
  }

  .header-nav-btn {
    font-size: 0.8rem;
    padding: var(--spacing-xs) var(--spacing-sm);
  }

  .header-main-title {
    font-size: 1.6rem;
  }
}

@media (max-width: 992px) {
  .ultra-modern-header {
    min-height: 120px;
    padding: var(--spacing-lg) 0;
  }

  .header-container {
    min-height: 100px;
    padding: 0 var(--spacing-md);
  }

  .header-top-row {
    height: 50px;
    margin-bottom: var(--spacing-md);
  }

  .header-logo {
    width: 60px;
    height: 60px;
  }

  .visitor-photo-container {
    width: 70px;
    height: 70px;
  }

  .header-photo-btn {
    width: 20px;
    height: 20px;
    font-size: 0.6rem;
  }

  .header-main-content {
    height: 40px;
  }

  .header-main-title {
    font-size: 1.4rem;
  }
}

@media (max-width: 768px) {
  .ultra-modern-header {
    min-height: 160px;
    padding: var(--spacing-md) 0;
  }

  .header-container {
    min-height: 140px;
    gap: var(--spacing-md);
  }

  .header-top-row {
    height: 60px;
    margin-bottom: var(--spacing-sm);
  }

  .header-main-content {
    flex-direction: column;
    height: auto;
    gap: var(--spacing-md);
  }

  .header-center-section {
    position: static;
    transform: none;
    flex: none;
  }

  .header-right-section {
    position: static;
    transform: none;
    align-self: center;
  }

  .header-nav-buttons {
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--spacing-xs);
  }

  .header-nav-btn {
    font-size: 0.75rem;
    padding: 6px 10px;
  }
}

@media (max-width: 480px) {
  .ultra-modern-header {
    min-height: 180px;
  }

  .header-container {
    min-height: 160px;
    padding: 0 var(--spacing-sm);
  }

  .header-logo {
    width: 50px;
    height: 50px;
  }

  .visitor-photo-container {
    width: 60px;
    height: 60px;
  }

  .photo-icon {
    font-size: 1rem;
  }

  .photo-text {
    font-size: 0.6rem;
  }

  .header-photo-btn {
    width: 18px;
    height: 18px;
    font-size: 0.55rem;
  }

  .header-main-title {
    font-size: 1.2rem;
  }

  .header-nav-btn {
    font-size: 0.7rem;
    padding: 4px 8px;
  }

  .header-nav-buttons {
    gap: 4px;
  }
}

/* ===== SECTION PHOTO SÉPARÉE ===== */
.photo-section-container {
  background: white;
  border-bottom: 1px solid var(--gray-200);
  padding: var(--spacing-lg) 0;
}

.photo-section-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
}

.visitor-photo-container {
  width: 120px;
  height: 120px;
  border-radius: var(--border-radius-xl);
  overflow: hidden;
  border: 3px solid var(--gray-200);
  background: var(--gray-50);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-normal);
  flex-shrink: 0;
}

.visitor-photo-container:hover {
  border-color: var(--primary-color);
  transform: scale(1.02);
}

.visitor-photo-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--gray-500);
  font-size: 0.875rem;
  text-align: center;
}

.photo-icon {
  font-size: 2rem;
}

.photo-text {
  font-size: 0.75rem;
  font-weight: 500;
}

.visitor-photo-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.photo-actions {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.photo-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--primary-color);
  background: white;
  color: var(--primary-color);
  border-radius: var(--border-radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
  text-decoration: none;
}

.photo-btn:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.photo-btn-remove {
  border-color: var(--danger-color);
  color: var(--danger-color);
}

.photo-btn-remove:hover {
  background: var(--danger-color);
  color: white;
}

.camera-section {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-2xl);
  z-index: 1000;
  max-width: 500px;
  width: 90%;
}

.camera-video {
  width: 100%;
  height: auto;
  border-radius: var(--border-radius-md);
  background: var(--gray-100);
  margin-bottom: var(--spacing-md);
}

.camera-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
}

/* ===== PHOTO VISITEUR DANS HEADER ===== */
.header-visitor-photo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
}

.visitor-photo-container {
  width: 100px;
  height: 100px;
  border-radius: var(--border-radius-xl);
  overflow: hidden;
  border: 3px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-normal);
}

.visitor-photo-container:hover {
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.05);
}

.visitor-photo-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.75rem;
}

.photo-icon {
  font-size: 1.5rem;
}

.visitor-photo-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.header-photo-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.header-photo-btn {
  width: 32px;
  height: 32px;
  border-radius: var(--border-radius-md);
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 0.875rem;
  cursor: pointer;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-photo-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.header-photo-btn-remove {
  background: rgba(220, 38, 38, 0.3);
}

.header-photo-btn-remove:hover {
  background: rgba(220, 38, 38, 0.5);
}

/* ===== BOUTONS D'ACTION HEADER ===== */
.header-action-buttons {
  display: flex;
  gap: var(--spacing-sm);
}

.header-action-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-md);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: var(--transition-normal);
  border: 1px solid transparent;
}

.header-action-btn.primary {
  background: white;
  color: var(--primary-color);
  border-color: white;
}

.header-action-btn.primary:hover {
  background: var(--gray-100);
  transform: translateY(-1px);
}

.header-action-btn.secondary {
  background: transparent;
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

.header-action-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

/* ===== CONTENEUR PRINCIPAL AVEC HEADER ===== */
.modern-form-container-with-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: calc(100vh - 140px);
  padding-top: var(--spacing-lg);
}

.modern-form-wrapper {
  width: 100%;
  position: relative;
}

/* ===== AMÉLIORATIONS FORMULAIRE AVEC TAILWIND ===== */
.ultra-form-section {
  background: white;
  border-radius: var(--border-radius-xl);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(229, 231, 235, 0.8);
  transition: all 0.3s ease;
  overflow: hidden;
}

.ultra-form-section:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
}

.ultra-section-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
  border-bottom: 1px solid rgba(229, 231, 235, 0.8);
}

.ultra-section-title {
  color: var(--gray-800);
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.ultra-section-description {
  color: var(--gray-600);
  line-height: 1.6;
  margin-top: var(--spacing-sm);
}

/* ===== CHAMPS DE FORMULAIRE AMÉLIORÉS ===== */
.ultra-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.ultra-input:hover {
  border-color: #6b7280;
}

.ultra-input.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.ultra-input.success {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* ===== LABELS AMÉLIORÉS ===== */
.ultra-label {
  font-weight: 600;
  color: var(--gray-700);
  font-size: 0.875rem;
  letter-spacing: 0.025em;
  transition: color 0.2s ease;
}

.ultra-label-required::after {
  content: ' *';
  color: #ef4444;
  font-weight: 700;
}

/* ===== AIDE ET VALIDATION ===== */
.field-help {
  font-size: 0.75rem;
  color: var(--gray-500);
  margin-top: 0.25rem;
  line-height: 1.4;
}

.field-error {
  font-size: 0.75rem;
  color: #ef4444;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.field-success {
  font-size: 0.75rem;
  color: #10b981;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* ===== ANIMATIONS FORMULAIRE ===== */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.ultra-form-section {
  animation: slideInUp 0.4s ease-out;
}

.ultra-form-section:nth-child(2) {
  animation-delay: 0.1s;
}

.ultra-form-section:nth-child(3) {
  animation-delay: 0.2s;
}

.ultra-form-section:nth-child(4) {
  animation-delay: 0.3s;
}

/* ===== RESPONSIVE AMÉLIORÉ ===== */
@media (max-width: 768px) {
  .modern-form-container-with-header {
    padding-top: var(--spacing-md);
  }

  .ultra-form-grid {
    grid-template-columns: 1fr !important;
    gap: var(--spacing-md);
  }

  .ultra-section-header {
    padding: var(--spacing-lg);
  }

  .ultra-section-title {
    font-size: 1.25rem;
  }
}

/* ===== FORMULAIRE ULTRA-MODERNE ===== */
.ultra-form-sections {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
}

.ultra-form-section {
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
}

.ultra-form-section:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--primary-light);
}

.ultra-section-header {
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 2px solid var(--gray-100);
}

.ultra-section-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--gray-800);
  margin-bottom: var(--spacing-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.ultra-section-description {
  color: var(--gray-600);
  font-size: 0.95rem;
  line-height: 1.6;
}

/* ===== GRILLE DE FORMULAIRE ===== */
.ultra-form-grid {
  display: grid;
  gap: var(--spacing-lg);
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.ultra-field-half {
  grid-column: span 1;
}

.ultra-field-full {
  grid-column: 1 / -1;
}

.ultra-field-two-thirds {
  grid-column: span 2;
}

.ultra-field-third {
  grid-column: span 1;
}

/* ===== GROUPES DE CHAMPS ===== */
.ultra-field-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.ultra-label {
  font-weight: 600;
  color: var(--gray-700);
  font-size: 0.875rem;
  letter-spacing: 0.025em;
}

.ultra-label-required::after {
  content: ' *';
  color: var(--danger-color);
  font-weight: 700;
}

.ultra-input {
  padding: var(--spacing-md);
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius-lg);
  font-size: 1rem;
  transition: var(--transition-normal);
  background: white;
  color: var(--gray-800);
}

.ultra-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.ultra-input:hover {
  border-color: var(--gray-300);
}

.ultra-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-md) center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 3rem;
}

.ultra-textarea {
  min-height: 120px;
  resize: vertical;
  font-family: inherit;
}

/* ===== STYLES POUR CHAMPS MODERNES ===== */
.modern-field-half,
.modern-field-third,
.modern-field-two-thirds {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.modern-label {
  font-weight: 600;
  color: var(--gray-700);
  font-size: 0.875rem;
}

.modern-input-wrapper {
  position: relative;
}

.modern-input {
  width: 100%;
  padding: var(--spacing-md);
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius-lg);
  font-size: 1rem;
  transition: var(--transition-normal);
  background: white;
}

.modern-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.modern-select-wrapper {
  position: relative;
}

.modern-select {
  width: 100%;
  padding: var(--spacing-md);
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius-lg);
  font-size: 1rem;
  background: white;
  appearance: none;
  cursor: pointer;
  transition: var(--transition-normal);
}

.modern-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.modern-select-icon {
  position: absolute;
  right: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  width: 1.25rem;
  height: 1.25rem;
  color: var(--gray-400);
  pointer-events: none;
}

/* ===== BOUTONS ULTRA-MODERNES ===== */
.ultra-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  border-radius: var(--border-radius-lg);
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
  min-width: 160px;
}

.ultra-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.ultra-btn:hover::before {
  left: 100%;
}

.ultra-btn-submit {
  background: linear-gradient(135deg, var(--success-color) 0%, var(--success-hover) 100%);
  color: white;
  box-shadow: var(--shadow-md);
}

.ultra-btn-submit:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.ultra-btn-print {
  background: linear-gradient(135deg, var(--warning-color) 0%, #f59e0b 100%);
  color: white;
  box-shadow: var(--shadow-md);
}

.ultra-btn-print:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.ultra-btn-cancel {
  background: linear-gradient(135deg, var(--gray-500) 0%, var(--gray-600) 100%);
  color: white;
  box-shadow: var(--shadow-md);
}

.ultra-btn-cancel:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* ===== BOUTONS GÉNÉRIQUES ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: 2px solid transparent;
  border-radius: var(--border-radius-lg);
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-normal);
  min-width: 140px;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: transparent;
  color: var(--gray-600);
  border-color: var(--gray-300);
}

.btn-secondary:hover {
  background: var(--gray-100);
  border-color: var(--gray-400);
  color: var(--gray-700);
}

.btn-success {
  background: var(--success-color);
  color: white;
  border-color: var(--success-color);
}

.btn-success:hover {
  background: var(--success-hover);
  border-color: var(--success-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-warning {
  background: var(--warning-color);
  color: white;
  border-color: var(--warning-color);
}

.btn-warning:hover {
  background: #b45309;
  border-color: #b45309;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* ===== ACTIONS DE FORMULAIRE ===== */
.ultra-form-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-2xl);
  padding-top: var(--spacing-xl);
  border-top: 2px solid var(--gray-100);
}

/* ===== SECTIONS DE SÉCURITÉ ===== */
.ultra-security-rules {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.ultra-security-rule {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: var(--gray-25);
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius-lg);
  transition: var(--transition-normal);
}

.ultra-security-rule:hover {
  background: var(--primary-light);
  border-color: var(--primary-color);
}

.ultra-security-icon {
  font-size: 1.5rem;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-light);
  border-radius: var(--border-radius-full);
  flex-shrink: 0;
}

.ultra-security-content {
  flex: 1;
}

.ultra-security-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: var(--spacing-sm);
}

.ultra-security-description {
  color: var(--gray-600);
  margin-bottom: var(--spacing-md);
  line-height: 1.6;
}

.ultra-security-checkbox {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.ultra-checkbox-security {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--primary-color);
  border-radius: var(--border-radius-sm);
  background: var(--primary-color);
}

.ultra-checkbox-label-security {
  font-size: 0.875rem;
  color: var(--gray-600);
  font-weight: 500;
}

/* ===== STYLES POUR LA PAGE PHOTO ===== */
.photo-page {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.photo-container {
  background: white;
  border-radius: var(--border-radius-2xl);
  box-shadow: var(--shadow-2xl);
  padding: var(--spacing-2xl);
  max-width: 800px;
  width: 100%;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.photo-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
}

.photo-header {
  margin-bottom: var(--spacing-xl);
}

.photo-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--gray-800);
  margin-bottom: var(--spacing-sm);
}

.photo-subtitle {
  color: var(--gray-600);
  font-size: 1rem;
}

/* ===== SECTION D'IMPRESSION - COMPLÈTEMENT INVISIBLE À L'ÉCRAN ===== */
.print-only-section {
  display: none !important;
  visibility: hidden !important;
  position: absolute !important;
  left: -9999px !important;
  top: -9999px !important;
  width: 0 !important;
  height: 0 !important;
  overflow: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
  z-index: -9999 !important;
}

/* S'assurer qu'aucun contenu d'impression n'affecte le layout */
.print-only-section * {
  display: none !important;
  visibility: hidden !important;
}

/* ===== ÉLÉMENTS VISIBLES UNIQUEMENT À L'IMPRESSION ===== */
.print-only {
  display: none !important;
}

.screen-only {
  display: block;
}

@media print {
  /* Masquer COMPLÈTEMENT tous les éléments d'écran */
  .ultra-modern-header,
  .modern-form-container-with-header,
  .header-nav-buttons,
  .header-photo-actions,
  .ultra-form-actions,
  .no-print,
  .screen-only {
    display: none !important;
    visibility: hidden !important;
  }

  /* Afficher UNIQUEMENT la section d'impression */
  .print-only-section {
    display: block !important;
    visibility: visible !important;
    position: static !important;
    left: auto !important;
    top: auto !important;
    width: 100% !important;
    height: auto !important;
    overflow: visible !important;
    margin: 0;
    padding: 0;
  }

  .print-only {
    display: block !important;
  }

  /* Styles généraux pour l'impression */
  body {
    background: white !important;
    color: black !important;
    font-size: 11pt;
    line-height: 1.4;
    margin: 0;
    padding: 1cm;
  }

  /* Header d'impression */
  .ultra-print-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 3px solid black;
  }

  .ultra-print-logo img {
    width: 80px;
    height: 80px;
    object-fit: contain;
  }

  .ultra-print-title-section {
    text-align: center;
    flex: 1;
    padding: 0 1rem;
  }

  .ultra-print-title {
    font-size: 24pt;
    font-weight: bold;
    margin: 0 0 0.5rem 0;
    color: black;
    letter-spacing: 1px;
  }

  .ultra-print-subtitle {
    font-size: 18pt;
    font-weight: 600;
    margin: 0 0 1rem 0;
    color: black;
  }

  .ultra-print-details {
    margin-top: 1rem;
    line-height: 1.4;
  }

  .ultra-print-department {
    font-size: 12pt;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    color: black;
  }

  .ultra-print-classification {
    font-size: 11pt;
    font-weight: 500;
    margin: 0 0 0.25rem 0;
    color: #333;
  }

  .ultra-print-date {
    font-size: 11pt;
    margin: 0 0 0.25rem 0;
    color: #333;
  }

  .ultra-print-reference {
    font-size: 11pt;
    font-weight: 500;
    margin: 0;
    color: #333;
  }

  .ultra-print-photo {
    width: 100px;
    height: 100px;
    border: 2px solid black;
    border-radius: 8px;
    overflow: hidden;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10pt;
    color: #666;
  }

  .ultra-print-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* Contenu principal */
  .ultra-print-content {
    margin-bottom: 2rem;
  }

  .ultra-print-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem 2rem;
    margin-bottom: 2rem;
  }

  .ultra-print-row {
    display: flex;
    justify-content: space-between;
    padding: 0.25rem 0;
    border-bottom: 1px dotted #ccc;
  }

  .ultra-print-label {
    font-weight: bold;
    color: black;
    min-width: 120px;
  }

  .ultra-print-value {
    flex: 1;
    text-align: right;
    color: black;
  }

  /* Section sécurité */
  .ultra-print-security-section {
    margin: 2rem 0;
    padding: 1rem;
    border: 1px solid #ccc;
    background: #f9f9f9;
  }

  .ultra-print-security-title {
    font-size: 14pt;
    font-weight: bold;
    margin: 0 0 1rem 0;
    color: black;
  }

  .ultra-print-security-list {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
  }

  .ultra-print-security-item {
    font-size: 10pt;
    color: black;
    padding: 0.25rem 0;
  }

  /* Signatures */
  .ultra-print-signatures {
    display: flex;
    justify-content: space-between;
    margin-top: 3rem;
    gap: 2rem;
  }

  .ultra-signature-box {
    flex: 1;
    text-align: center;
  }

  .ultra-signature-title {
    font-weight: bold;
    margin-bottom: 2rem;
    font-size: 12pt;
    color: black;
  }

  .ultra-signature-line {
    border-bottom: 2px solid black;
    height: 3rem;
    margin-bottom: 0.5rem;
  }

  .ultra-signature-date {
    font-size: 10pt;
    color: #666;
  }



  /* Optimisation pour l'impression couleur */
  * {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  /* Éviter les coupures de page */
  .ultra-print-header,
  .ultra-print-security-section,
  .ultra-print-signatures {
    page-break-inside: avoid;
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .header-main-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--spacing-lg);
  }

  .header-right-section {
    align-items: center;
  }

  .header-nav-buttons {
    justify-content: center;
  }

  .ultra-form-grid {
    grid-template-columns: 1fr;
  }

  .ultra-field-two-thirds,
  .ultra-field-third {
    grid-column: 1;
  }

  .ultra-form-actions {
    flex-direction: column;
    align-items: center;
  }

  .ultra-btn,
  .btn {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .header-container {
    padding: 0 var(--spacing-md);
  }

  .modern-form-container-with-header {
    padding: var(--spacing-lg);
    margin: var(--spacing-md);
  }

  .ultra-form-section {
    padding: var(--spacing-lg);
  }

  .photo-container {
    padding: var(--spacing-lg);
    margin: var(--spacing-sm);
  }
}

/* ===== ANIMATIONS ET TRANSITIONS ===== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

/* ===== ACCESSIBILITÉ ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

*:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
  border-radius: var(--border-radius-sm);
}

/* ===== UTILITAIRES ===== */
.hidden {
  display: none !important;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* ===== CLASSES MANQUANTES POUR CAMÉRA ===== */
.header-camera-section {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-xl);
  z-index: 1000;
  min-width: 300px;
}

.header-camera-video {
  width: 100%;
  height: auto;
  border-radius: var(--border-radius-md);
  background: var(--gray-100);
}

.header-camera-actions {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
  justify-content: center;
}

.photo-text {
  font-size: 0.75rem;
  color: inherit;
}

/* ===== CLASSES POUR RESPONSIVE GRID ===== */
@media (min-width: 768px) {
  .ultra-form-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .modern-field-two-thirds {
    grid-column: span 2;
  }

  .modern-field-third {
    grid-column: span 1;
  }

  .modern-field-half {
    grid-column: span 1;
  }
}

@media (min-width: 1024px) {
  .ultra-form-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .modern-field-two-thirds {
    grid-column: span 2;
  }

  .modern-field-third {
    grid-column: span 1;
  }

  .modern-field-half {
    grid-column: span 1;
  }
}

/* ===== CLASSES BREADCRUMB ===== */
.breadcrumb-item {
  display: inline;
}

.breadcrumb-separator {
  margin: 0 var(--spacing-xs);
}

.breadcrumb-item.active {
  font-weight: 600;
}

/* ===== CLASSES POUR NAVIGATION ===== */
.header-nav-btn.accueil:hover {
  background: rgba(34, 197, 94, 0.2);
}

.header-nav-btn.liste:hover {
  background: rgba(59, 130, 246, 0.2);
}

.header-nav-btn.recherche:hover {
  background: rgba(168, 85, 247, 0.2);
}

.header-nav-btn.rapports:hover {
  background: rgba(245, 158, 11, 0.2);
}

/* ===== AMÉLIORATION DES PRINT STYLES ===== */
.ultra-print-value {
  font-weight: normal;
}

.ultra-print-label {
  font-weight: 600;
}

.ultra-print-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-xs);
  padding: var(--spacing-xs) 0;
  border-bottom: 1px dotted var(--gray-300);
}

/* ===== CORRECTIONS POUR COMPATIBILITÉ ===== */
.modern-form-wrapper {
  width: 100%;
}

.ultra-select {
  cursor: pointer;
}

.ultra-textarea {
  font-family: inherit;
  line-height: 1.5;
}

/* ===== ÉTATS FOCUS AMÉLIORÉS ===== */
.ultra-input:focus,
.modern-input:focus,
.modern-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
  outline: none;
}

/* ===== ÉTATS DISABLED ===== */
.ultra-input:disabled,
.modern-input:disabled,
.modern-select:disabled {
  background-color: var(--gray-100);
  color: var(--gray-500);
  cursor: not-allowed;
  opacity: 0.6;
}

.ultra-btn:disabled,
.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.ultra-btn:disabled:hover,
.btn:disabled:hover {
  transform: none !important;
  box-shadow: none !important;
}

/* ===== STYLES POUR PAGE LOGIN ===== */
.modern-body {
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
  min-height: 100vh;
}

.login-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
  padding: var(--spacing-lg) 0;
  box-shadow: var(--shadow-lg);
}

/* ===== STYLES POUR PAGE ACCUEIL ===== */
.accueil-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
}

.accueil-header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-logo-section {
  display: flex;
  align-items: center;
}

/* ===== STYLES POUR ÉLÉMENTS COMMUNS ===== */
.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  position: relative;
  z-index: 1;
}

/* ===== CORRECTIONS FINALES ===== */
.ultra-form-grid {
  display: grid;
  gap: var(--spacing-lg);
  grid-template-columns: 1fr;
}

/* Responsive pour les grilles */
@media (min-width: 640px) {
  .ultra-form-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .ultra-field-full {
    grid-column: 1 / -1;
  }
}

@media (min-width: 1024px) {
  .ultra-form-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .ultra-field-two-thirds {
    grid-column: span 2;
  }

  .ultra-field-third {
    grid-column: span 1;
  }

  .ultra-field-half {
    grid-column: span 1;
  }

  .ultra-field-full {
    grid-column: 1 / -1;
  }
}

/* ===== AMÉLIORATION DES TRANSITIONS ===== */
.ultra-input,
.modern-input,
.modern-select,
.ultra-btn,
.btn,
.header-nav-btn,
.header-photo-btn {
  transition: var(--transition-normal);
}

/* ===== STYLES POUR COMPATIBILITÉ TAILWIND ===== */
.bg-blue-50 {
  background-color: #eff6ff;
}

.border-blue-200 {
  border-color: #bfdbfe;
}

.text-blue-800 {
  color: #1e40af;
}

.text-blue-700 {
  color: #1d4ed8;
}

.text-green-600 {
  color: #059669;
}

.text-red-700 {
  color: #b91c1c;
}

.bg-red-100 {
  background-color: #fee2e2;
}

.border-red-400 {
  border-color: #f87171;
}

.bg-gray-100 {
  background-color: var(--gray-100);
}

.border-gray-300 {
  border-color: var(--gray-300);
}

.text-gray-600 {
  color: var(--gray-600);
}

.rounded-lg {
  border-radius: var(--border-radius-lg);
}

.p-4 {
  padding: 1rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.space-y-1 > * + * {
  margin-top: 0.25rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.w-3 {
  width: 0.75rem;
}

.h-3 {
  height: 0.75rem;
}

.w-48 {
  width: 12rem;
}

.h-48 {
  height: 12rem;
}

.rounded-full {
  border-radius: var(--border-radius-full);
}

.bg-gray-400 {
  background-color: var(--gray-400);
}

.bg-yellow-400 {
  background-color: #fbbf24;
}

.bg-green-400 {
  background-color: #4ade80;
}

.bg-red-400 {
  background-color: #f87171;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}
