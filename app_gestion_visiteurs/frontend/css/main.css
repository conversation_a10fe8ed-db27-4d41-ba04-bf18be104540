/*
 * Styles principaux pour l'application de gestion des visiteurs
 * DCOP 413 - Design moderne et cohérent
 * Compatible avec Tailwind CSS
 */

/* Variables CSS centralisées dans professional-design.css */
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --transition: all 0.2s ease-in-out;
}

/* STYLES DE BASE */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: var(--gray-800);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* STYLES PERSONNALISÉS POUR AMÉLIORER TAILWIND */

/* Amélioration des transitions */
.transition-all {
  transition: var(--transition);
}

/* Amélioration des ombres */
.shadow-custom {
  box-shadow: var(--shadow-lg);
}

.shadow-custom-xl {
  box-shadow: var(--shadow-xl);
}

.shadow-custom-2xl {
  box-shadow: var(--shadow-2xl);
}

/* Amélioration des focus states */
.focus-ring:focus {
  outline: none;
  ring-width: 2px;
  ring-color: var(--primary-color);
  ring-opacity: 0.5;
}

/* Styles pour les boutons avec effets */
.btn-hover-lift:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

/* Styles pour les cartes */
.card-hover:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-2px);
}

/* STYLES POUR L'IMPRESSION */
@media print {
  body {
    background: white;
    color: black;
    font-size: 12pt;
    line-height: 1.4;
  }

  .no-print {
    display: none;
  }

  .print-break {
    page-break-before: always;
  }

  .print-break-after {
    page-break-after: always;
  }

  /* Assurer que les couleurs sont visibles à l'impression */
  * {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
}

/* STYLES RESPONSIFS */
@media (max-width: 640px) {
  .responsive-text {
    font-size: 0.875rem;
  }

  .responsive-padding {
    padding: 1rem;
  }

  .responsive-margin {
    margin: 0.5rem;
  }
}
@media screen and (min-width: 411px) and (max-width: 413px) and (min-height: 914px) and (max-height: 916px) {
  header.flex {
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    padding: 1rem !important;
    gap: 0.75rem !important;
  }

  header .h-28 {
    width: 90px !important;
    height: 90px !important;
    margin: 0 auto !important;
    order: 1 !important;
  }

  header .flex-1 {
    width: 100% !important;
    text-align: center !important;
    order: 2 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .photo-section {
    margin: 0 auto !important;
    width: 150px !important;
    order: 3 !important;
  }

  h1 {
    font-size: 1.625rem !important;
    text-align: center !important;
  }
}

/* 412px x 883px */
@media screen and (min-width: 411px) and (max-width: 413px) and (min-height: 882px) and (max-height: 884px) {
  header.flex {
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    padding: 1rem !important;
    gap: 0.75rem !important;
  }

  header .h-28 {
    width: 90px !important;
    height: 90px !important;
    margin: 0 auto !important;
    order: 1 !important;
  }

  header .flex-1 {
    width: 100% !important;
    text-align: center !important;
    order: 2 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .photo-section {
    margin: 0 auto !important;
    width: 150px !important;
    order: 3 !important;
  }

  h1 {
    font-size: 1.625rem !important;
    text-align: center !important;
  }
}

/* 360px x 760px */
@media screen and (min-width: 359px) and (max-width: 361px) and (min-height: 759px) and (max-height: 761px) {
  header.flex {
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    padding: 0.875rem !important;
    gap: 0.5rem !important;
  }

  header .h-28 {
    width: 80px !important;
    height: 80px !important;
    margin: 0 auto !important;
    order: 1 !important;
  }

  header .flex-1 {
    width: 100% !important;
    text-align: center !important;
    order: 2 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .photo-section {
    margin: 0 auto !important;
    width: 130px !important;
    order: 3 !important;
  }

  h1 {
    font-size: 1.5rem !important;
    text-align: center !important;
  }
}

/* 360px x 800px */
@media screen and (min-width: 359px) and (max-width: 361px) and (min-height: 799px) and (max-height: 801px) {
  header.flex {
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    padding: 0.875rem !important;
    gap: 0.5rem !important;
  }

  header .h-28 {
    width: 80px !important;
    height: 80px !important;
    margin: 0 auto !important;
    order: 1 !important;
  }

  header .flex-1 {
    width: 100% !important;
    text-align: center !important;
    order: 2 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .photo-section {
    margin: 0 auto !important;
    width: 130px !important;
    order: 3 !important;
  }

  h1 {
    font-size: 1.5rem !important;
    text-align: center !important;
  }
}

/* 384px x 854px */
@media screen and (min-width: 383px) and (max-width: 385px) and (min-height: 853px) and (max-height: 855px) {
  header.flex {
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    padding: 1rem !important;
    gap: 0.625rem !important;
  }

  header .h-28 {
    width: 85px !important;
    height: 85px !important;
    margin: 0 auto !important;
    order: 1 !important;
  }

  header .flex-1 {
    width: 100% !important;
    text-align: center !important;
    order: 2 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .photo-section {
    margin: 0 auto !important;
    width: 140px !important;
    order: 3 !important;
  }

  h1 {
    font-size: 1.5625rem !important;
    text-align: center !important;
  }
}

/* 315px x 812px */
@media screen and (min-width: 314px) and (max-width: 316px) and (min-height: 811px) and (max-height: 813px) {
  header.flex {
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    padding: 0.75rem !important;
    gap: 0.5rem !important;
  }

  header .h-28 {
    width: 75px !important;
    height: 75px !important;
    margin: 0 auto !important;
    order: 1 !important;
  }

  header .flex-1 {
    width: 100% !important;
    text-align: center !important;
    order: 2 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .photo-section {
    margin: 0 auto !important;
    width: 120px !important;
    order: 3 !important;
  }

  h1 {
    font-size: 1.375rem !important;
    text-align: center !important;
  }
}

/* 414px x 896px */
@media screen and (min-width: 413px) and (max-width: 415px) and (min-height: 895px) and (max-height: 897px) {
  header.flex {
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    padding: 1rem !important;
    gap: 0.75rem !important;
  }

  header .h-28 {
    width: 90px !important;
    height: 90px !important;
    margin: 0 auto !important;
    order: 1 !important;
  }

  header .flex-1 {
    width: 100% !important;
    text-align: center !important;
    order: 2 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .photo-section {
    margin: 0 auto !important;
    width: 145px !important;
    order: 3 !important;
  }

  h1 {
    font-size: 1.625rem !important;
    text-align: center !important;
  }
}

/* 390px x 884px */
@media screen and (min-width: 389px) and (max-width: 391px) and (min-height: 883px) and (max-height: 885px) {
  header.flex {
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    padding: 1rem !important;
    gap: 0.625rem !important;
  }

  header .h-28 {
    width: 85px !important;
    height: 85px !important;
    margin: 0 auto !important;
    order: 1 !important;
  }

  header .flex-1 {
    width: 100% !important;
    text-align: center !important;
    order: 2 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .photo-section {
    margin: 0 auto !important;
    width: 140px !important;
    order: 3 !important;
  }

  h1 {
    font-size: 1.5625rem !important;
    text-align: center !important;
  }
}

/* 375px x 812px */
@media screen and (min-width: 374px) and (max-width: 376px) and (min-height: 811px) and (max-height: 813px) {
  header.flex {
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    padding: 0.875rem !important;
    gap: 0.5rem !important;
  }

  header .h-28 {
    width: 80px !important;
    height: 80px !important;
    margin: 0 auto !important;
    order: 1 !important;
  }

  header .flex-1 {
    width: 100% !important;
    text-align: center !important;
    order: 2 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .photo-section {
    margin: 0 auto !important;
    width: 135px !important;
    order: 3 !important;
  }

  h1 {
    font-size: 1.5rem !important;
    text-align: center !important;
  }
}

/* 375px x 667px */
@media screen and (min-width: 374px) and (max-width: 376px) and (min-height: 666px) and (max-height: 668px) {
  header.flex {
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    padding: 0.875rem !important;
    gap: 0.5rem !important;
  }

  header .h-28 {
    width: 80px !important;
    height: 80px !important;
    margin: 0 auto !important;
    order: 1 !important;
  }

  header .flex-1 {
    width: 100% !important;
    text-align: center !important;
    order: 2 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .photo-section {
    margin: 0 auto !important;
    width: 135px !important;
    order: 3 !important;
  }

  h1 {
    font-size: 1.5rem !important;
    text-align: center !important;
  }
}

/* 428px x 926px */
@media screen and (min-width: 427px) and (max-width: 429px) and (min-height: 925px) and (max-height: 927px) {
  header.flex {
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    padding: 1.125rem !important;
    gap: 0.875rem !important;
  }

  header .h-28 {
    width: 95px !important;
    height: 95px !important;
    margin: 0 auto !important;
    order: 1 !important;
  }

  header .flex-1 {
    width: 100% !important;
    text-align: center !important;
    order: 2 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .photo-section {
    margin: 0 auto !important;
    width: 155px !important;
    order: 3 !important;
  }

  h1 {
    font-size: 1.75rem !important;
    text-align: center !important;
  }
}

/* Styles communs pour tous les mobiles */
@media screen and (max-width: 428px) {
  p {
    font-size: 0.875rem !important;
    text-align: center !important;
  }

  .photo-section button {
    font-size: 0.875rem !important;
    padding: 0.375rem 0.75rem !important;
  }
}

/* STYLES TABLETTES - LOGO À GAUCHE, TEXTE AU CENTRE, PHOTO À DROITE */
/* 810px x 1080px */
@media screen and (min-width: 809px) and (max-width: 811px) and (min-height: 1079px) and (max-height: 1081px) {
  header.flex {
    flex-direction: row !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 1.5rem !important;
    gap: 1.5rem !important;
  }

  header .h-28 {
    width: 100px !important;
    height: 100px !important;
    margin: 0 !important;
    flex-shrink: 0 !important;
    order: 1 !important;
  }

  header .flex-1 {
    text-align: center !important;
    padding: 0 1rem !important;
    flex-grow: 1 !important;
    order: 2 !important;
  }

  .photo-section {
    margin: 0 !important;
    width: 150px !important;
    flex-shrink: 0 !important;
    order: 3 !important;
  }

  h1 {
    font-size: 1.875rem !important;
    text-align: center !important;
  }

  p {
    font-size: 0.9375rem !important;
    text-align: center !important;
  }
}

/* 800px x 1280px */
@media screen and (min-width: 799px) and (max-width: 801px) and (min-height: 1279px) and (max-height: 1281px) {
  header.flex {
    flex-direction: row !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 1.5rem !important;
    gap: 1.5rem !important;
  }

  header .h-28 {
    width: 100px !important;
    height: 100px !important;
    margin: 0 !important;
    flex-shrink: 0 !important;
    order: 1 !important;
  }

  header .flex-1 {
    text-align: center !important;
    padding: 0 1rem !important;
    flex-grow: 1 !important;
    order: 2 !important;
  }

  .photo-section {
    margin: 0 !important;
    width: 150px !important;
    flex-shrink: 0 !important;
    order: 3 !important;
  }

  h1 {
    font-size: 1.875rem !important;
    text-align: center !important;
  }

  p {
    font-size: 0.9375rem !important;
    text-align: center !important;
  }
}

/* Style général pour les tablettes (≥ 800px) */
@media screen and (min-width: 800px) {
  header.flex {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    gap: 2rem;
  }

  header .h-28 {
    width: 112px;
    height: 112px;
    margin: 0;
    flex-shrink: 0;
  }

  header .flex-1 {
    text-align: center;
    padding: 0 1rem;
    flex-grow: 1;
    max-width: 50%;
  }

  .photo-section {
    margin: 0;
    width: 160px;
    flex-shrink: 0;
  }

  h1 {
    font-size: 2rem !important;
    line-height: 1.2;
  }

  p {
    font-size: 1rem;
  }
}

/* Styles intermédiaires (429px - 799px) */
@media screen and (min-width: 429px) and (max-width: 799px) {
  header.flex {
    flex-direction: column;
    align-items: center;
    padding: 1.25rem;
  }

  header .h-28 {
    width: 96px;
    height: 96px;
    margin: 0 auto;
  }

  header .flex-1 {
    width: 100%;
    text-align: center;
    padding: 0.75rem 0;
  }

  .photo-section {
    margin: 0.75rem auto;
    width: 150px;
  }

  h1 {
    font-size: 1.75rem !important;
    line-height: 1.2;
  }

  p {
    font-size: 0.9375rem;
  }
}

/* Optimisations pour les hauteurs spécifiques */
@media screen and (max-height: 800px) {
  header.flex {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  .photo-section {
    margin-top: 0.25rem;
    margin-bottom: 0.25rem;
  }
}

/* AMÉLIORATION DE L'ACCESSIBILITÉ */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* FOCUS VISIBLE POUR L'ACCESSIBILITÉ */
*:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* ANIMATIONS PERFORMANTES */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* STYLES POUR LES BOUTONS AVEC SÉCURITÉ */
button {
  cursor: pointer;
  transition: var(--transition);
  border: none;
  font-family: inherit;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* STYLES POUR LES INPUTS SÉCURISÉS */
input, textarea, select {
  font-family: inherit;
  transition: var(--transition);
}

input:invalid, textarea:invalid {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 1px var(--danger-color);
}

input:valid, textarea:valid {
  border-color: var(--success-color);
}

/* PROTECTION CONTRE LES INJECTIONS CSS */
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="date"],
textarea {
  background-color: white;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius);
  padding: 0.75rem;
}

/* SÉCURISATION DES IMAGES */
img {
  max-width: 100%;
  height: auto;
  object-fit: cover;
  border-radius: var(--border-radius);
  /* Protection contre les attaques par images */
  image-rendering: auto;
  /* Empêcher le drag & drop non autorisé */
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

/* STYLES POUR LES FORMULAIRES SÉCURISÉS */
.form-group {
  position: relative;
  margin-bottom: 1.5rem;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group input:invalid,
.form-group textarea:invalid {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.form-group input:valid,
.form-group textarea:valid {
  border-color: var(--success-color);
}

/* MESSAGES D'ERREUR */
.error-message {
  font-size: 0.875rem;
  font-weight: 500;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.error-message::before {
  content: "⚠️";
  font-size: 1rem;
}

.error-message.hidden {
  display: none;
}

/* INDICATEUR DE CHARGEMENT SÉCURISÉ */
#loadingIndicator {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

#loadingIndicator.hidden {
  display: none !important;
}

#loadingIndicator:not(.hidden) {
  display: flex !important;
}

/* PROTECTION CONTRE LES INJECTIONS CSS */
* {
  /* Empêcher l'injection de contenu malveillant */
  content: none;
}

*::before,
*::after {
  /* Contrôler strictement le contenu généré */
  content: attr(data-content, "");
}

/* STYLES POUR L'IMPRESSION SÉCURISÉE */
@media print {
  /* Masquer les éléments sensibles lors de l'impression */
  .no-print,
  button,
  input[type="file"],
  .error-message {
    display: none !important;
  }

  /* Optimiser pour l'impression */
  body {
    background: white !important;
    color: black !important;
    font-size: 12pt;
    line-height: 1.4;
  }

  /* Éviter les coupures de page dans les sections importantes */
  .page-break-avoid {
    page-break-inside: avoid;
    break-inside: avoid;
  }

  /* Forcer les couleurs pour l'impression */
  * {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
    print-color-adjust: exact !important;
  }
}

/* STYLES POUR LES COMPOSANTS INTERACTIFS SÉCURISÉS */

/* Boutons sécurisés avec protection contre le double-clic */
.btn-secure {
  position: relative;
  overflow: hidden;
  transition: var(--transition);
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.btn-secure:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.btn-secure:active {
  transform: translateY(1px);
}

/* Protection contre les attaques de timing */
.btn-secure::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.btn-secure:active::after {
  width: 300px;
  height: 300px;
}

/* Zone de photo sécurisée */
.photo-section {
  position: relative;
  /* Empêcher les attaques par drag & drop */
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

.photo-section img {
  /* Protection contre les images malveillantes */
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
  /* Empêcher l'exécution de scripts dans les images SVG */
  pointer-events: none;
}

/* Validation visuelle en temps réel */
.input-valid {
  border-color: var(--success-color) !important;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23059669'%3e%3cpath d='M12.736 3.97a.733.733 0 0 1 1.047 0c.286.289.29.756.01 1.05L7.88 12.01a.733.733 0 0 1-1.065.02L3.217 8.384a.757.757 0 0 1 0-1.06.733.733 0 0 1 1.047 0l3.052 3.093 5.4-6.425a.247.247 0 0 1 .02-.022Z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem 1rem;
}

.input-invalid {
  border-color: var(--danger-color) !important;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23dc2626'%3e%3cpath d='M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z'/%3e%3cpath d='M7.002 11a1 1 0 1 1 2 0 1 1 0 0 1-2 0zM7.1 4.995a.905.905 0 1 1 1.8 0l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 4.995z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem 1rem;
}

/* Styles pour les notifications sécurisées */
.notification {
  position: fixed;
  top: 1rem;
  right: 1rem;
  max-width: 400px;
  padding: 1rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
}

.notification.show {
  transform: translateX(0);
}

.notification.success {
  background-color: var(--success-color);
  color: white;
}

.notification.error {
  background-color: var(--danger-color);
  color: white;
}

.notification.warning {
  background-color: var(--warning-color);
  color: white;
}

.notification.info {
  background-color: var(--primary-color);
  color: white;
}

/* Protection contre les attaques de redirection */
a[href^="javascript:"],
a[href^="data:"],
a[href^="vbscript:"] {
  pointer-events: none !important;
  color: var(--danger-color) !important;
  text-decoration: line-through !important;
}

a[href^="javascript:"]::after,
a[href^="data:"]::after,
a[href^="vbscript:"]::after {
  content: " [LIEN BLOQUÉ]";
  color: var(--danger-color);
  font-weight: bold;
  font-size: 0.75rem;
}

/* Styles responsives sécurisés */
@media (max-width: 768px) {
  /* Ajustements pour mobile avec sécurité */
  .form-group input,
  .form-group textarea,
  .form-group select {
    font-size: 16px; /* Empêcher le zoom automatique sur iOS */
  }

  /* Protection contre les attaques de viewport */
  body {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
  }
}

/* Protection contre les attaques par CSS */
@supports (display: contents) {
  /* Utiliser des fonctionnalités modernes uniquement si supportées */
  .modern-layout {
    display: contents;
  }
}

/* Désactiver les fonctionnalités potentiellement dangereuses */
iframe,
object,
embed {
  display: none !important;
}

/* Protection contre l'injection de contenu */
[contenteditable="true"] {
  -webkit-user-modify: read-only;
  -moz-user-modify: read-only;
  user-modify: read-only;
}

/* Styles pour le mode sombre sécurisé */
@media (prefers-color-scheme: dark) {
  :root {
    --gray-50: #1f2937;
    --gray-100: #111827;
    --gray-200: #374151;
    --gray-300: #4b5563;
    --gray-700: #d1d5db;
    --gray-800: #f9fafb;
  }

  body {
    background-color: var(--gray-100);
    color: var(--gray-800);
  }

  /* Maintenir la lisibilité en mode sombre */
  input, textarea, select {
    background-color: var(--gray-50);
    color: var(--gray-800);
    border-color: var(--gray-300);
  }
}

/* STYLES POUR L'IMPRESSION - VERSION PROFESSIONNELLE */
@media print {
  @page {
    size: A4 portrait;
    margin: 1.5cm 1.2cm 1.2cm 1.2cm;
  }

  /* Forçage maximal des couleurs pour l'impression */
  * {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  html {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  /* Centrage parfait du document */
  html, body {
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    height: auto !important;
  }



  /* Optimisation de l'espace et centrage parfait */
  form {
    max-width: none !important;
    margin: 0 auto !important;
    padding: 0 !important;
    background: white !important;
    box-shadow: none !important;
    border: none !important;
    border-radius: 0 !important;
    width: 100% !important;
    position: relative !important;
  }

  /* Centrage du contenu principal */
  body {
    background: white !important;
    padding: 0 !important;
    margin: 0 auto !important;
    color: #1a1a1a !important;
    font-size: 10pt !important;
    line-height: 1.4 !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    max-width: 21cm !important;
    min-height: 29.7cm !important;
  }

  /* En-tête professionnel avec espace optimisé pour la photo */
  header {
    position: relative !important;
    border-bottom: 3px solid #2563eb !important;
    padding: 15px 140px 15px 15px !important;
    margin-bottom: 20px !important;
    min-height: 160px !important;
    display: flex !important;
    align-items: center !important;
    page-break-inside: avoid !important;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    overflow: visible !important;
  }

  /* Logo avec style amélioré */
  .h-28 {
    height: 70px !important;
    width: 70px !important;
    margin-right: 20px !important;
    border: 2px solid #2563eb !important;
    border-radius: 50% !important;
    padding: 5px !important;
    background: white !important;
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.2) !important;
  }

  /* Titre avec style professionnel */
  header .flex-1 {
    text-align: center !important;
    margin: 0 20px !important;
  }

  h1 {
    font-size: 22pt !important;
    font-weight: 700 !important;
    color: #1e293b !important;
    margin: 0 0 5px 0 !important;
    line-height: 1.2 !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
  }

  header p {
    font-size: 11pt !important;
    color: #64748b !important;
    margin: 0 !important;
    font-weight: 500 !important;
  }

  /* Photo du visiteur - Position parfaite pour impression */
  .photo-section {
    position: absolute !important;
    top: 5px !important;
    right: 5px !important;
    width: 120px !important;
    height: 150px !important;
    z-index: 20 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .photo-section > div {
    background: white !important;
    border: 3px solid #1e40af !important;
    border-radius: 6px !important;
    padding: 3px !important;
    width: 100% !important;
    height: 100% !important;
    box-shadow: 0 3px 10px rgba(30, 64, 175, 0.4) !important;
    position: relative !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  /* Suppression du pseudo-élément pour éviter les conflits d'impression */
  .photo-section > div::before {
    display: none !important;
  }

  /* Photo optimisée pour l'impression */
  #visitorPhoto {
    display: block !important;
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    object-position: center !important;
    border-radius: 3px !important;
    border: 1px solid #e5e7eb !important;
  }

  /* Placeholder optimisé pour l'impression */
  #photoPlaceholder {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    height: 100% !important;
    width: 100% !important;
    font-size: 8pt !important;
    color: #6b7280 !important;
    text-align: center !important;
    line-height: 1.2 !important;
  }

  #photoPlaceholder .w-16 {
    width: 32px !important;
    height: 32px !important;
    margin-bottom: 6px !important;
  }

  #photoPlaceholder .w-8 {
    width: 20px !important;
    height: 20px !important;
  }

  #photoPlaceholder p {
    margin: 0 !important;
    font-size: 7pt !important;
    font-weight: 500 !important;
  }

  .photo-section button {
    display: none !important;
  }

  #visitorPhoto {
    display: block !important;
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    border-radius: 2px !important;
  }

  #photoPlaceholder {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    height: 100% !important;
    font-size: 8pt !important;
    color: #666 !important;
    text-align: center !important;
  }

  /* Sections avec design professionnel */
  section {
    margin-bottom: 18px !important;
    padding: 16px !important;
    border-radius: 8px !important;
    page-break-inside: avoid !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08) !important;
    border: 2px solid transparent !important;
  }

  /* Couleurs des sections avec gradients subtils */
  section.bg-gray-50 {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
    border-color: #cbd5e1 !important;
  }

  section.bg-blue-50 {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%) !important;
    border-color: #93c5fd !important;
  }

  section.bg-yellow-50 {
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%) !important;
    border-color: #fbbf24 !important;
  }

  section.bg-green-50 {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%) !important;
    border-color: #86efac !important;
  }

  /* Badges numérotés avec design 3D */
  .bg-blue-600 {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%) !important;
    color: white !important;
    box-shadow: 0 3px 8px rgba(37, 99, 235, 0.4) !important;
    border: 2px solid #1e40af !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3) !important;
  }

  .bg-yellow-600 {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #b45309 100%) !important;
    color: white !important;
    box-shadow: 0 3px 8px rgba(217, 119, 6, 0.4) !important;
    border: 2px solid #92400e !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3) !important;
  }

  .bg-green-600 {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 50%, #15803d 100%) !important;
    color: white !important;
    box-shadow: 0 3px 8px rgba(22, 163, 74, 0.4) !important;
    border: 2px solid #166534 !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3) !important;
  }

  /* Titres de sections avec style élégant */
  h2 {
    font-size: 14pt !important;
    font-weight: 700 !important;
    color: #1e293b !important;
    margin: 0 0 12px 0 !important;
    line-height: 1.3 !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
  }

  h3 {
    font-size: 11pt !important;
    font-weight: 600 !important;
    color: #475569 !important;
    margin: 0 0 8px 0 !important;
    padding-bottom: 4px !important;
    border-bottom: 2px solid #e2e8f0 !important;
    position: relative !important;
  }

  h3::after {
    content: "" !important;
    position: absolute !important;
    bottom: -2px !important;
    left: 0 !important;
    width: 30px !important;
    height: 2px !important;
    background: #2563eb !important;
  }

  /* Grilles et champs */
  .grid {
    display: grid !important;
    gap: 8px !important;
  }

  .grid-cols-1 {
    grid-template-columns: 1fr !important;
  }

  .grid-cols-2 {
    grid-template-columns: 1fr 1fr !important;
  }

  .grid-cols-3 {
    grid-template-columns: 1fr 1fr 1fr !important;
  }

  /* Labels avec style professionnel */
  label {
    font-size: 9pt !important;
    font-weight: 600 !important;
    color: #1e293b !important;
    margin-bottom: 3px !important;
    display: block !important;
    position: relative !important;
  }

  label::before {
    content: "▸" !important;
    color: #2563eb !important;
    margin-right: 4px !important;
    font-size: 8pt !important;
  }

  /* Inputs avec design élégant */
  input, textarea {
    border: 2px solid #e2e8f0 !important;
    border-radius: 4px !important;
    padding: 6px 8px !important;
    font-size: 9pt !important;
    background: #fafafa !important;
    width: 100% !important;
    box-sizing: border-box !important;
    transition: all 0.2s ease !important;
  }

  input:focus, textarea:focus {
    border-color: #2563eb !important;
    background: white !important;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1) !important;
  }

  textarea {
    min-height: 70px !important;
    resize: none !important;
    line-height: 1.4 !important;
  }

  /* Listes avec puces élégantes */
  .w-2.h-2.bg-yellow-600 {
    background: radial-gradient(circle, #f59e0b 0%, #d97706 100%) !important;
    box-shadow: 0 1px 3px rgba(217, 119, 6, 0.4) !important;
    border: 1px solid #b45309 !important;
  }

  .w-2.h-2.bg-red-500 {
    background: radial-gradient(circle, #f87171 0%, #ef4444 100%) !important;
    box-shadow: 0 1px 3px rgba(239, 68, 68, 0.4) !important;
    border: 1px solid #dc2626 !important;
  }

  /* Amélioration des icônes d'alerte */
  .bg-yellow-600.w-6.h-6 {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
    box-shadow: 0 2px 6px rgba(217, 119, 6, 0.3) !important;
    border: 2px solid #b45309 !important;
  }

  /* Style pour les checkboxes et radios */
  input[type="checkbox"], input[type="radio"] {
    width: 14px !important;
    height: 14px !important;
    margin-right: 6px !important;
    border: 2px solid #2563eb !important;
    background: white !important;
  }

  input[type="checkbox"]:checked, input[type="radio"]:checked {
    background: #2563eb !important;
    border-color: #1d4ed8 !important;
  }

  /* Masquer les éléments non imprimables */
  .hidden-print {
    display: none !important;
  }

  /* Afficher les checkboxes et radios pour l'impression */
  input[type="checkbox"], input[type="radio"] {
    display: inline !important;
    width: 12px !important;
    height: 12px !important;
    margin-right: 4px !important;
  }

  /* Optimisation de l'espace avec rythme visuel */
  .space-y-2 > * + * {
    margin-top: 6px !important;
  }

  .space-y-4 > * + * {
    margin-top: 10px !important;
  }

  .space-y-6 > * + * {
    margin-top: 14px !important;
  }

  /* Amélioration des grilles */
  .grid {
    display: grid !important;
    gap: 12px !important;
  }

  /* Espacement spécial pour les sous-sections */
  .mb-8 {
    margin-bottom: 16px !important;
  }

  .mb-6 {
    margin-bottom: 12px !important;
  }

  .mb-4 {
    margin-bottom: 8px !important;
  }

  /* Padding pour les conteneurs blancs */
  .bg-white {
    background: #ffffff !important;
    border: 1px solid #e2e8f0 !important;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.05) !important;
  }

  /* Éviter les coupures de page inappropriées */
  .page-break-avoid {
    page-break-inside: avoid !important;
  }

  /* Forcer une nouvelle page si nécessaire */
  .page-break-before {
    page-break-before: always !important;
  }

  /* Bordures et séparateurs */
  .border-b {
    border-bottom: 1px solid #e5e7eb !important;
  }

  .border-t {
    border-top: 1px solid #e5e7eb !important;
  }

  /* Améliorations finales pour un rendu premium */

  /* Filigrane et numérotation de page */
  @page {
    @bottom-center {
      content: "Document officiel - DCOP 413 - Page " counter(page) " sur " counter(pages);
      font-size: 8pt;
      color: #64748b !important;
      font-style: italic;
      font-family: 'Segoe UI', sans-serif;
    }

    @top-right {
      content: "Confidentiel";
      font-size: 7pt;
      color: #ef4444 !important;
      font-weight: bold;
    }
  }

  /* Amélioration du centrage global */
  .max-w-4xl {
    max-width: none !important;
    width: 100% !important;
    margin: 0 !important;
  }

  /* Amélioration des bordures */
  .border-b {
    border-bottom: 2px solid #e2e8f0 !important;
    position: relative !important;
  }

  .border-b::after {
    content: "" !important;
    position: absolute !important;
    bottom: -2px !important;
    left: 0 !important;
    width: 50px !important;
    height: 2px !important;
    background: #2563eb !important;
  }

  /* Style pour les listes avec icônes */
  ul li {
    position: relative !important;
    padding-left: 8px !important;
  }

  /* Amélioration des fieldsets */
  fieldset {
    border: 2px solid #e2e8f0 !important;
    border-radius: 6px !important;
    padding: 8px 12px !important;
    background: #fafafa !important;
  }

  legend {
    background: white !important;
    padding: 0 8px !important;
    font-weight: 600 !important;
    color: #1e293b !important;
  }

  /* Effet de profondeur pour le formulaire principal */
  form {
    position: relative !important;
  }

  form::before {
    content: "" !important;
    position: absolute !important;
    top: -5px !important;
    left: -5px !important;
    right: -5px !important;
    bottom: -5px !important;
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0) !important;
    border-radius: 12px !important;
    z-index: -1 !important;
  }
}

/* STYLES POUR L'ÉCRAN - Préservation des couleurs */
@media screen {
  * {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
  
  /* Assurer que les couleurs sont visibles à l'écran aussi */
  .bg-gray-50 {
    background-color: #f9fafb;
  }
  
  .bg-blue-50 {
    background-color: #eff6ff;
  }
  
  .bg-yellow-50 {
    background-color: #fffbeb;
  }
  
  .bg-green-50 {
    background-color: #f0fdf4;
  }
  
  .bg-blue-600 {
    background-color: #2563eb;
  }
  
  .bg-yellow-600 {
    background-color: #d97706;
  }
  
  .bg-green-600 {
    background-color: #16a34a;
  }
}
