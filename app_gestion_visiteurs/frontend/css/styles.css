/* Import du CSS Tailwind généré */
@import url('./tailwind.css');

/*
 * Styles personnalisés pour le formulaire de réception des visiteurs
 * Version sécurisée et optimisée - DCOP 413
 * Conforme aux standards d'accessibilité WCAG 2.1 AA
 */

/* Variables CSS définies dans professional-design.css */

/* STYLES GÉNÉRAUX AVEC PERFORMANCE OPTIMISÉE */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: var(--gray-800);
  background-color: var(--gray-100);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* AMÉLIORATION DE L'ACCESSIBILITÉ */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* FOCUS VISIBLE POUR L'ACCESSIBILITÉ */
*:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* ANIMATIONS PERFORMANTES */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* STYLES POUR LES BOUTONS AVEC SÉCURITÉ */
button {
  cursor: pointer;
  transition: var(--transition);
  border: none;
  font-family: inherit;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* STYLES POUR LES INPUTS SÉCURISÉS */
input, textarea, select {
  font-family: inherit;
  transition: var(--transition);
}

input:invalid, textarea:invalid {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 1px var(--danger-color);
}

input:valid, textarea:valid {
  border-color: var(--success-color);
}

/* PROTECTION CONTRE LES INJECTIONS CSS */
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="date"],
textarea {
  background-color: white;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius);
  padding: 0.75rem;
}

/* SÉCURISATION DES IMAGES */
img {
  max-width: 100%;
  height: auto;
  object-fit: cover;
  border-radius: var(--border-radius);
}

/* STYLES POUR L'IMPRESSION - VERSION PREMIUM OPTIMISÉE */
@media print {
  /* Configuration de page optimisée pour 2 pages maximum */
  @page {
    size: A4 portrait;
    margin: 0.8cm 1cm 0.8cm 1cm; /* Marges réduites pour plus d'espace */
  }

  /* Forçage maximal des couleurs pour l'impression */
  * {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  html {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  /* Configuration globale pour optimiser l'espace */
  html, body {
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    height: auto !important;
    font-size: 9pt !important; /* Taille réduite pour plus de contenu */
    line-height: 1.3 !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
  }

  /* Conteneur principal optimisé */
  body {
    background: white !important;
    color: #1a1a1a !important;
    max-width: 19.2cm !important; /* Largeur optimisée */
    margin: 0 auto !important;
    padding: 0.2cm !important;
  }

  /* Formulaire principal */
  form {
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
    background: white !important;
    box-shadow: none !important;
    border: none !important;
    border-radius: 0 !important;
    width: 100% !important;
    position: relative !important;
    space-y: 0 !important;
  }

  /* En-tête compact avec photo intégrée */
  header {
    position: relative !important;
    border-bottom: 2px solid #2563eb !important;
    padding: 8px 130px 8px 8px !important;
    margin-bottom: 12px !important;
    min-height: 120px !important;
    display: flex !important;
    align-items: center !important;
    page-break-inside: avoid !important;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
    border-radius: 6px !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
    overflow: visible !important;
  }

  /* Logo optimisé */
  .h-28 {
    height: 50px !important;
    width: 50px !important;
    margin-right: 12px !important;
    border: 2px solid #2563eb !important;
    border-radius: 50% !important;
    padding: 3px !important;
    background: white !important;
    box-shadow: 0 1px 4px rgba(37, 99, 235, 0.2) !important;
  }

  /* Titre compact */
  header .flex-1 {
    text-align: center !important;
    margin: 0 15px !important;
  }

  h1 {
    font-size: 18pt !important;
    font-weight: 700 !important;
    color: #1e293b !important;
    margin: 0 0 3px 0 !important;
    line-height: 1.1 !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
  }

  header p {
    font-size: 9pt !important;
    color: #64748b !important;
    margin: 0 !important;
    font-weight: 500 !important;
  }

  /* Photo du visiteur - Limitée strictement à la zone d'en-tête */
  .photo-section {
    position: absolute !important;
    top: 10px !important;
    right: 10px !important;
    width: 100px !important;
    height: 120px !important;
    z-index: 10 !important; /* Réduit pour rester sous l'en-tête */
    margin: 0 !important;
    padding: 0 !important;
    max-width: 100px !important; /* Limite stricte */
    max-height: 120px !important; /* Limite stricte */
  }

  .photo-section > div {
    background: white !important;
    border: 2px solid #1e40af !important;
    border-radius: 4px !important;
    padding: 0 !important;
    width: 100% !important;
    height: 100% !important;
    box-shadow: 0 1px 4px rgba(30, 64, 175, 0.3) !important;
    position: relative !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    overflow: hidden !important;
    max-width: 100px !important; /* Limite stricte du conteneur */
    max-height: 120px !important; /* Limite stricte du conteneur */
  }

  /* Photo avec contraintes strictes pour rester dans l'en-tête */
  #visitorPhoto {
    display: block !important;
    width: calc(100% - 4px) !important;
    height: calc(100% - 4px) !important;
    object-fit: cover !important;
    object-position: center center !important;
    border-radius: 2px !important;
    border: none !important;
    image-rendering: -webkit-optimize-contrast !important;
    image-rendering: crisp-edges !important;
    position: absolute !important;
    top: 2px !important;
    left: 2px !important;
    right: 2px !important;
    bottom: 2px !important;
    max-width: 96px !important; /* Contrainte stricte */
    max-height: 116px !important; /* Contrainte stricte */
  }

  /* Placeholder photo avec contraintes */
  #photoPlaceholder {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    height: 100% !important;
    width: 100% !important;
    font-size: 6pt !important;
    color: #6b7280 !important;
    text-align: center !important;
    line-height: 1.1 !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    max-width: 100px !important;
    max-height: 120px !important;
  }

  #photoPlaceholder .w-16 {
    width: 20px !important;
    height: 20px !important;
    margin-bottom: 3px !important;
  }

  #photoPlaceholder .w-8 {
    width: 14px !important;
    height: 14px !important;
  }

  #photoPlaceholder p {
    margin: 0 !important;
    font-size: 5pt !important;
    font-weight: 500 !important;
  }

  .photo-section button {
    display: none !important;
  }

  /* Contraintes strictes pour empêcher tout débordement */
  .photo-section img {
    max-width: 96px !important;
    max-height: 116px !important;
    min-width: auto !important;
    min-height: auto !important;
  }

  /* S'assurer que l'en-tête contient bien la photo */
  header {
    position: relative !important;
    overflow: hidden !important; /* Empêche tout débordement de la photo */
    border-bottom: 3px solid #2563eb !important;
    padding: 15px 120px 15px 15px !important; /* Espace réservé pour la photo */
    margin-bottom: 20px !important;
    min-height: 140px !important; /* Hauteur minimale pour contenir la photo */
    display: flex !important;
    align-items: center !important;
    page-break-inside: avoid !important;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
  }

  /* Sections compactes avec couleurs préservées */
  section {
    margin-bottom: 10px !important;
    padding: 8px !important;
    border-radius: 6px !important;
    page-break-inside: avoid !important;
    box-shadow: 0 1px 4px rgba(0,0,0,0.08) !important;
    border: 1px solid transparent !important;
  }

  /* Couleurs des sections avec gradients subtils */
  section.bg-gray-50 {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
    border-color: #cbd5e1 !important;
  }

  section.bg-blue-50 {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%) !important;
    border-color: #93c5fd !important;
  }

  section.bg-yellow-50 {
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%) !important;
    border-color: #fbbf24 !important;
  }

  section.bg-green-50 {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%) !important;
    border-color: #86efac !important;
  }

  /* Badges numérotés avec couleurs vives */
  .bg-blue-600 {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%) !important;
    color: white !important;
    box-shadow: 0 2px 6px rgba(37, 99, 235, 0.4) !important;
    border: 2px solid #1e40af !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3) !important;
  }

  .bg-yellow-600 {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #b45309 100%) !important;
    color: white !important;
    box-shadow: 0 2px 6px rgba(217, 119, 6, 0.4) !important;
    border: 2px solid #92400e !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3) !important;
  }

  .bg-green-600 {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 50%, #15803d 100%) !important;
    color: white !important;
    box-shadow: 0 2px 6px rgba(22, 163, 74, 0.4) !important;
    border: 2px solid #166534 !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3) !important;
  }

  /* Titres de sections compacts */
  h2 {
    font-size: 11pt !important;
    font-weight: 700 !important;
    color: #1e293b !important;
    margin: 0 0 6px 0 !important;
    line-height: 1.2 !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
  }

  h3 {
    font-size: 9pt !important;
    font-weight: 600 !important;
    color: #475569 !important;
    margin: 0 0 4px 0 !important;
    padding-bottom: 2px !important;
    border-bottom: 1px solid #e2e8f0 !important;
    position: relative !important;
  }

  h3::after {
    content: "" !important;
    position: absolute !important;
    bottom: -1px !important;
    left: 0 !important;
    width: 20px !important;
    height: 1px !important;
    background: #2563eb !important;
  }

  /* Grilles compactes */
  .grid {
    display: grid !important;
    gap: 4px !important;
  }

  .grid-cols-1 {
    grid-template-columns: 1fr !important;
  }

  .grid-cols-2 {
    grid-template-columns: 1fr 1fr !important;
  }

  .grid-cols-3 {
    grid-template-columns: 1fr 1fr 1fr !important;
  }

  /* Labels compacts */
  label {
    font-size: 8pt !important;
    font-weight: 600 !important;
    color: #1e293b !important;
    margin-bottom: 2px !important;
    display: block !important;
    position: relative !important;
  }

  label::before {
    content: "▸" !important;
    color: #2563eb !important;
    margin-right: 3px !important;
    font-size: 7pt !important;
  }

  /* Inputs compacts */
  input, textarea {
    border: 1px solid #e2e8f0 !important;
    border-radius: 3px !important;
    padding: 3px 5px !important;
    font-size: 8pt !important;
    background: #fafafa !important;
    width: 100% !important;
    box-sizing: border-box !important;
    transition: all 0.2s ease !important;
  }

  input:focus, textarea:focus {
    border-color: #2563eb !important;
    background: white !important;
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1) !important;
  }

  textarea {
    min-height: 40px !important;
    resize: none !important;
    line-height: 1.3 !important;
  }

  /* Listes avec puces colorées */
  .w-2.h-2.bg-yellow-600 {
    background: radial-gradient(circle, #f59e0b 0%, #d97706 100%) !important;
    box-shadow: 0 1px 2px rgba(217, 119, 6, 0.4) !important;
    border: 1px solid #b45309 !important;
  }

  .w-2.h-2.bg-red-500 {
    background: radial-gradient(circle, #f87171 0%, #ef4444 100%) !important;
    box-shadow: 0 1px 2px rgba(239, 68, 68, 0.4) !important;
    border: 1px solid #dc2626 !important;
  }

  /* Icônes d'alerte colorées */
  .bg-yellow-600.w-6.h-6 {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
    box-shadow: 0 1px 4px rgba(217, 119, 6, 0.3) !important;
    border: 2px solid #b45309 !important;
  }

  /* Checkboxes et radios */
  input[type="checkbox"], input[type="radio"] {
    width: 10px !important;
    height: 10px !important;
    margin-right: 4px !important;
    border: 1px solid #2563eb !important;
    background: white !important;
  }

  input[type="checkbox"]:checked, input[type="radio"]:checked {
    background: #2563eb !important;
    border-color: #1d4ed8 !important;
  }

  /* Espacement optimisé */
  .space-y-2 > * + * {
    margin-top: 3px !important;
  }

  .space-y-4 > * + * {
    margin-top: 6px !important;
  }

  .space-y-6 > * + * {
    margin-top: 8px !important;
  }

  .space-y-12 > * + * {
    margin-top: 8px !important; /* Réduit pour économiser l'espace */
  }

  /* Marges réduites */
  .mb-8 {
    margin-bottom: 8px !important;
  }

  .mb-6 {
    margin-bottom: 6px !important;
  }

  .mb-4 {
    margin-bottom: 4px !important;
  }

  .mb-12 {
    margin-bottom: 8px !important;
  }

  /* Conteneurs blancs avec bordures subtiles */
  .bg-white {
    background: #ffffff !important;
    border: 1px solid #e2e8f0 !important;
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.05) !important;
  }

  /* Gestion des sauts de page pour 2 pages maximum */
  .page-break-avoid {
    page-break-inside: avoid !important;
  }

  /* Forcer une nouvelle page après la section 2 */
  section.bg-blue-50 {
    page-break-after: page !important;
  }

  /* Bordures et séparateurs */
  .border-b {
    border-bottom: 1px solid #e5e7eb !important;
  }

  .border-t {
    border-top: 1px solid #e5e7eb !important;
  }

  /* Fieldsets compacts */
  fieldset {
    border: 1px solid #e2e8f0 !important;
    border-radius: 4px !important;
    padding: 4px 6px !important;
    background: #fafafa !important;
  }

  legend {
    background: white !important;
    padding: 0 4px !important;
    font-weight: 600 !important;
    color: #1e293b !important;
    font-size: 8pt !important;
  }

  /* Masquer les éléments non imprimables */
  .hidden-print {
    display: none !important;
  }

  /* Numérotation de page */
  @page {
    @bottom-center {
      content: "Document officiel - DCOP 413 - Page " counter(page) " / 2";
      font-size: 7pt;
      color: #64748b !important;
      font-style: italic;
      font-family: 'Segoe UI', sans-serif;
    }

    @top-right {
      content: "Confidentiel";
      font-size: 6pt;
      color: #ef4444 !important;
      font-weight: bold;
    }
  }

  /* Optimisation finale pour 2 pages */
  .max-w-4xl {
    max-width: none !important;
    width: 100% !important;
    margin: 0 !important;
  }
}

  /* STYLES POUR L'ÉCRAN - Préservation des couleurs */
  @media screen {
    * {
      -webkit-print-color-adjust: exact;
      print-color-adjust: exact;
      color-adjust: exact;
    }
  }

