/*
 * CSS perfectionné pour l'application de gestion des visiteurs
 * DCOP 413 - Design moderne et responsive
 * Compatible avec Tailwind CSS - Optimisé pour l'UX
 */

/* Variables CSS centralisées dans professional-design.css */

/* STYLES DE BASE PERFECTIONNÉS */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-weight: 400;
  line-height: 1.6;
  color: var(--gray-800);
  background-color: var(--gray-50);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  overflow-x: hidden;
}

/* TYPOGRAPHIE AMÉLIORÉE */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.025em;
  color: var(--gray-900);
}

h1 { font-size: 2.25rem; }
h2 { font-size: 1.875rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
  margin-bottom: var(--spacing-md);
  color: var(--gray-600);
}

/* LIENS AMÉLIORÉS */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition-fast);
}

a:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

/* ANIMATIONS ET TRANSITIONS AVANCÉES */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInFromLeft {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInFromRight {
  from { opacity: 0; transform: translateX(20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translate3d(0, 0, 0); }
  40%, 43% { transform: translate3d(0, -8px, 0); }
  70% { transform: translate3d(0, -4px, 0); }
  90% { transform: translate3d(0, -2px, 0); }
}



/* CLASSES D'ANIMATION */
.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-in-left {
  animation: slideInFromLeft 0.5s ease-out;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.5s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}



/* TRANSITIONS PERFECTIONNÉES */
.transition-fast {
  transition: var(--transition-fast);
}

.transition-normal {
  transition: var(--transition-normal);
}

.transition-slow {
  transition: var(--transition-slow);
}

.transition-bounce {
  transition: var(--transition-bounce);
}

/* EFFETS HOVER AVANCÉS */
.hover-lift {
  transition: var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.hover-lift-strong {
  transition: var(--transition-normal);
}

.hover-lift-strong:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.hover-scale {
  transition: var(--transition-normal);
}

.hover-scale:hover {
  transform: scale(1.02);
}

.hover-glow {
  transition: var(--transition-normal);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(37, 99, 235, 0.3);
}

/* STYLES POUR LES FORMULAIRES PERFECTIONNÉS */
.form-group {
  position: relative;
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: var(--spacing-sm);
  transition: var(--transition-fast);
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius-lg);
  background-color: var(--gray-50);
  font-size: 1rem;
  line-height: 1.5;
  color: var(--gray-900);
  transition: var(--transition-normal);
  box-shadow: var(--shadow-xs);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  background-color: white;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1), var(--shadow-sm);
  transform: translateY(-1px);
}

.form-input:hover:not(:focus) {
  border-color: var(--gray-400);
  background-color: white;
}

.form-input::placeholder {
  color: var(--gray-400);
  font-style: italic;
}

.form-input.error {
  border-color: var(--danger-color);
  background-color: var(--danger-light);
}

.form-input.success {
  border-color: var(--success-color);
  background-color: var(--success-light);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

.form-error {
  display: block;
  margin-top: var(--spacing-xs);
  font-size: 0.875rem;
  color: var(--danger-color);
  font-weight: 500;
}

.form-help {
  display: block;
  margin-top: var(--spacing-xs);
  font-size: 0.875rem;
  color: var(--gray-500);
}

/* STYLES POUR LES BOUTONS PERFECTIONNÉS */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius-lg);
  font-size: 1rem;
  font-weight: 600;
  line-height: 1;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-normal);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  color: white;
  border: 1px solid var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-active) 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: white;
  color: var(--gray-700);
  border: 1px solid var(--gray-300);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--gray-50);
  border-color: var(--gray-400);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-success {
  background: linear-gradient(135deg, var(--success-color) 0%, var(--success-hover) 100%);
  color: white;
  border: 1px solid var(--success-color);
}

.btn-success:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-danger {
  background: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-hover) 100%);
  color: white;
  border: 1px solid var(--danger-color);
}

.btn-danger:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

.btn-icon {
  padding: 0.75rem;
  width: auto;
  aspect-ratio: 1;
}

/* STYLES POUR LES CARTES PERFECTIONNÉES */
.card {
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius-2xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-25);
}

.card-body {
  padding: var(--spacing-lg);
}

.card-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--gray-200);
  background: var(--gray-25);
}

.card-elevated {
  box-shadow: var(--shadow-lg);
}

.card-elevated:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px);
}

/* COMPOSANTS SPÉCIALISÉS */
.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-light) 0%, rgba(37, 99, 235, 0.1) 100%);
  border: 2px solid rgba(37, 99, 235, 0.2);
  border-radius: var(--border-radius-full);
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  overflow: hidden;
}

.logo-container:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.logo-container img {
  object-fit: contain;
  border-radius: var(--border-radius-full);
  transition: var(--transition-normal);
}



.photo-upload-zone {
  border: 2px dashed var(--gray-300);
  border-radius: var(--border-radius-xl);
  background: var(--gray-50);
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.photo-upload-zone:hover {
  border-color: var(--primary-color);
  background: var(--primary-light);
}

.photo-upload-zone.dragover {
  border-color: var(--primary-color);
  background: var(--primary-light);
  transform: scale(1.02);
}

/* ALERTES ET NOTIFICATIONS */
.alert {
  padding: var(--spacing-md);
  border-radius: var(--border-radius-lg);
  border: 1px solid;
  margin-bottom: var(--spacing-md);
  animation: fadeIn 0.3s ease-out;
}

.alert-success {
  background: var(--success-light);
  border-color: var(--success-color);
  color: var(--success-hover);
}

.alert-warning {
  background: var(--warning-light);
  border-color: var(--warning-color);
  color: #92400e;
}

.alert-danger {
  background: var(--danger-light);
  border-color: var(--danger-color);
  color: var(--danger-hover);
}

.alert-info {
  background: var(--primary-light);
  border-color: var(--primary-color);
  color: var(--primary-dark);
}

/* BADGES ET ÉTIQUETTES */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: var(--border-radius-full);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-primary {
  background: var(--primary-color);
  color: white;
}

.badge-success {
  background: var(--success-color);
  color: white;
}

.badge-warning {
  background: var(--warning-color);
  color: white;
}

.badge-danger {
  background: var(--danger-color);
  color: white;
}

/* STYLES RESPONSIFS AVANCÉS */
@media (max-width: 640px) {
  html {
    font-size: 14px;
  }

  .container {
    padding-left: var(--spacing-md);
    padding-right: var(--spacing-md);
  }

  .btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
  }

  .card {
    margin: var(--spacing-sm);
    border-radius: var(--border-radius-xl);
  }

  .card-body {
    padding: var(--spacing-md);
  }

  h1 { font-size: 1.875rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.25rem; }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .container {
    padding-left: var(--spacing-lg);
    padding-right: var(--spacing-lg);
  }
}

@media (min-width: 1025px) {
  .container {
    padding-left: var(--spacing-xl);
    padding-right: var(--spacing-xl);
  }
}

/* ACCESSIBILITÉ PERFECTIONNÉE */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

*:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
  border-radius: var(--border-radius-sm);
}

/* Amélioration du contraste pour l'accessibilité */
@media (prefers-contrast: high) {
  :root {
    --gray-300: #a1a1aa;
    --gray-400: #71717a;
    --gray-500: #52525b;
  }
}

/* Support pour les préférences de mouvement réduit */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Mode sombre (préparation) */
@media (prefers-color-scheme: dark) {
  :root {
    --gray-50: #18181b;
    --gray-100: #27272a;
    --gray-200: #3f3f46;
    --gray-300: #52525b;
    --gray-800: #e4e4e7;
    --gray-900: #f4f4f5;
  }
}

/* STYLES POUR L'IMPRESSION PERFECTIONNÉS */
@media print {
  * {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  body {
    background: white !important;
    color: black !important;
    font-size: 12pt;
    line-height: 1.4;
  }

  .no-print {
    display: none !important;
  }

  .print-break {
    page-break-before: always;
  }

  .print-break-after {
    page-break-after: always;
  }

  .card {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }

  .btn {
    display: none !important;
  }

  h1, h2, h3 {
    page-break-after: avoid;
  }

  img {
    max-width: 100% !important;
    page-break-inside: avoid;
  }
}

/* UTILITAIRES GÉNÉRAUX */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.hidden {
  display: none !important;
}

.visible {
  display: block !important;
}

.opacity-0 {
  opacity: 0;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-100 {
  opacity: 1;
}

/* STYLES POUR LES ÉLÉMENTS SPÉCIFIQUES */
input, textarea, select, button {
  font-family: inherit;
}

img {
  max-width: 100%;
  height: auto;
  object-fit: cover;
}



/* STYLES POUR LES TOOLTIPS */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltip-text {
  visibility: hidden;
  width: 200px;
  background-color: var(--gray-900);
  color: white;
  text-align: center;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm);
  position: absolute;
  z-index: 1000;
  bottom: 125%;
  left: 50%;
  margin-left: -100px;
  opacity: 0;
  transition: var(--transition-normal);
  font-size: 0.875rem;
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* AMÉLIORATION DES SCROLLBARS */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: var(--border-radius-md);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-400);
  border-radius: var(--border-radius-md);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-500);
}
