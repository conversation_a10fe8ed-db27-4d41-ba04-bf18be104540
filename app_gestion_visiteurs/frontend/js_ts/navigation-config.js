/**
 * Configuration de navigation optimisée - DCOP 413
 * Gestion intelligente des redirections selon l'état d'authentification
 */

'use strict';

// ===== CONFIGURATION DE NAVIGATION =====
class NavigationConfig {
  static ROUTES = {
    // Pages publiques (accessibles sans authentification)
    PUBLIC: {
      'index.html': {
        requireAuth: false,
        redirectIfAuth: 'accueil.html',
        alwaysRedirect: 'login.html' // index redirige toujours vers login
      },
      'login.html': {
        requireAuth: false,
        redirectIfAuth: 'accueil.html'
      }
    },
    
    // Pages protégées (nécessitent une authentification)
    PROTECTED: {
      'accueil.html': {
        requireAuth: true,
        redirectIfNoAuth: 'login.html',
        isHomePage: true
      },
      'main.html': {
        requireAuth: true,
        redirectIfNoAuth: 'login.html',
        parentPage: 'accueil.html'
      },
      'photo.html': {
        requireAuth: true,
        redirectIfNoAuth: 'login.html',
        parentPage: 'main.html'
      }
    }
  };

  static DEFAULT_ROUTES = {
    LOGIN_SUCCESS: 'accueil.html',
    LOGOUT: 'login.html',
    UNAUTHORIZED: 'login.html',
    HOME: 'accueil.html',
    ENTRY_POINT: 'login.html'
  };

  static getCurrentPage() {
    const path = window.location.pathname;
    return path.split('/').pop() || 'index.html';
  }

  static getPageConfig(pageName = null) {
    const currentPage = pageName || NavigationConfig.getCurrentPage();
    
    // Chercher dans les pages publiques
    if (NavigationConfig.ROUTES.PUBLIC[currentPage]) {
      return {
        ...NavigationConfig.ROUTES.PUBLIC[currentPage],
        type: 'public',
        page: currentPage
      };
    }
    
    // Chercher dans les pages protégées
    if (NavigationConfig.ROUTES.PROTECTED[currentPage]) {
      return {
        ...NavigationConfig.ROUTES.PROTECTED[currentPage],
        type: 'protected',
        page: currentPage
      };
    }
    
    // Page inconnue - traiter comme protégée par sécurité
    return {
      requireAuth: true,
      redirectIfNoAuth: NavigationConfig.DEFAULT_ROUTES.UNAUTHORIZED,
      type: 'unknown',
      page: currentPage
    };
  }

  static shouldRedirect(isAuthenticated, pageName = null) {
    const config = NavigationConfig.getPageConfig(pageName);
    
    // Redirection forcée (comme index.html)
    if (config.alwaysRedirect) {
      return {
        shouldRedirect: true,
        targetPage: config.alwaysRedirect,
        reason: 'always_redirect'
      };
    }
    
    // Page protégée sans authentification
    if (config.requireAuth && !isAuthenticated) {
      return {
        shouldRedirect: true,
        targetPage: config.redirectIfNoAuth || NavigationConfig.DEFAULT_ROUTES.UNAUTHORIZED,
        reason: 'auth_required'
      };
    }
    
    // Page publique avec authentification
    if (!config.requireAuth && isAuthenticated && config.redirectIfAuth) {
      return {
        shouldRedirect: true,
        targetPage: config.redirectIfAuth,
        reason: 'already_authenticated'
      };
    }
    
    return {
      shouldRedirect: false,
      reason: 'access_allowed'
    };
  }

  static performRedirect(targetPage, reason = '') {
    if (window.location.pathname.includes(targetPage)) {
      return; // Déjà sur la page cible
    }
    
    console.log(`🔄 Redirection vers ${targetPage} (${reason})`);
    
    // Utiliser replace pour éviter l'historique sur certaines redirections
    const useReplace = ['always_redirect', 'auth_required'].includes(reason);
    
    if (useReplace) {
      window.location.replace(targetPage);
    } else {
      window.location.href = targetPage;
    }
  }

  static handleNavigation(isAuthenticated, pageName = null) {
    const redirectInfo = NavigationConfig.shouldRedirect(isAuthenticated, pageName);
    
    if (redirectInfo.shouldRedirect) {
      NavigationConfig.performRedirect(redirectInfo.targetPage, redirectInfo.reason);
      return false; // Arrêter l'exécution
    }
    
    return true; // Continuer l'exécution
  }

  static getNavigationFlow() {
    return {
      entryPoint: NavigationConfig.DEFAULT_ROUTES.ENTRY_POINT,
      afterLogin: NavigationConfig.DEFAULT_ROUTES.LOGIN_SUCCESS,
      afterLogout: NavigationConfig.DEFAULT_ROUTES.LOGOUT,
      onUnauthorized: NavigationConfig.DEFAULT_ROUTES.UNAUTHORIZED,
      homePage: NavigationConfig.DEFAULT_ROUTES.HOME
    };
  }

  static isProtectedPage(pageName = null) {
    const config = NavigationConfig.getPageConfig(pageName);
    return config.requireAuth === true;
  }

  static isPublicPage(pageName = null) {
    const config = NavigationConfig.getPageConfig(pageName);
    return config.requireAuth === false;
  }

  static getParentPage(pageName = null) {
    const config = NavigationConfig.getPageConfig(pageName);
    return config.parentPage || NavigationConfig.DEFAULT_ROUTES.HOME;
  }

  static getBreadcrumb(pageName = null) {
    const currentPage = pageName || NavigationConfig.getCurrentPage();
    const config = NavigationConfig.getPageConfig(currentPage);
    const breadcrumb = [currentPage];
    
    if (config.parentPage) {
      breadcrumb.unshift(config.parentPage);
      
      // Récursif pour les parents de parents
      const parentConfig = NavigationConfig.getPageConfig(config.parentPage);
      if (parentConfig.parentPage) {
        breadcrumb.unshift(parentConfig.parentPage);
      }
    }
    
    return breadcrumb;
  }

  // Méthodes utilitaires pour l'intégration
  static createSecureLink(targetPage, currentAuth = null) {
    const isAuth = currentAuth !== null ? currentAuth : (window.AuthManager ? window.AuthManager.isAuthenticated() : false);
    const redirectInfo = NavigationConfig.shouldRedirect(isAuth, targetPage);
    
    if (redirectInfo.shouldRedirect) {
      return redirectInfo.targetPage;
    }
    
    return targetPage;
  }

  static validatePageAccess(pageName = null, isAuthenticated = null) {
    const isAuth = isAuthenticated !== null ? isAuthenticated : (window.AuthManager ? window.AuthManager.isAuthenticated() : false);
    const redirectInfo = NavigationConfig.shouldRedirect(isAuth, pageName);
    
    return {
      hasAccess: !redirectInfo.shouldRedirect,
      redirectInfo: redirectInfo,
      pageConfig: NavigationConfig.getPageConfig(pageName)
    };
  }
}

// Exposition globale
window.NavigationConfig = NavigationConfig;

// Export pour utilisation dans d'autres modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = NavigationConfig;
}
