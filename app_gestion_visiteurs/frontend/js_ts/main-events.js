/**
 * Gestionnaire d'événements pour la page formulaire principal
 * Séparation propre du JavaScript selon les bonnes pratiques
 */

'use strict';

// ===== GESTIONNAIRE D'ÉVÉNEMENTS FORMULAIRE =====
class MainEventManager {
  constructor() {
    this.init();
  }

  init() {
    this.checkAuthentication();
    this.bindEvents();
    this.initializeForm();
    this.loadFormData();
  }

  checkAuthentication() {
    // Vérifier l'authentification avant d'afficher la page
    if (window.AuthManager && !window.AuthManager.isAuthenticated()) {
      console.log('🔒 Accès non autorisé au formulaire - Redirection vers login');
      if (window.AuthManager.setRedirectUrl) {
        window.AuthManager.setRedirectUrl(window.location.href);
      }
      window.location.href = 'login.html';
      return false;
    }
    return true;
  }

  bindEvents() {
    // Événements pour les boutons d'action
    this.bindActionButtons();
    
    // Événements pour le formulaire
    this.bindFormEvents();
    
    // Événements pour la navigation
    this.bindNavigationEvents();
  }

  bindActionButtons() {
    const printBtn = document.getElementById('printBtn');
    const submitBtn = document.getElementById('submitBtn');
    const cancelBtn = document.querySelector('.ultra-btn-cancel');

    if (printBtn) {
      printBtn.addEventListener('click', this.handlePrint.bind(this));
    }

    if (cancelBtn) {
      cancelBtn.addEventListener('click', this.handleReturnToHome.bind(this));
    }

    // Le bouton submit est géré par l'événement submit du formulaire
  }

  bindFormEvents() {
    const form = document.getElementById('visitorForm');
    if (form) {
      form.addEventListener('input', this.handleFormInput.bind(this));
      form.addEventListener('submit', this.handleFormSubmit.bind(this));
    }
  }

  bindNavigationEvents() {
    // Gestion des liens de navigation avec alertes
    const devLinks = document.querySelectorAll('[data-dev-feature]');
    devLinks.forEach(link => {
      link.addEventListener('click', this.handleDevelopmentFeature.bind(this));
    });
  }

  initializeForm() {
    this.initializeDateTimeFields();
    this.setupFormValidation();
  }

  initializeDateTimeFields() {
    // Initialiser la date d'aujourd'hui
    const dateVisiteField = document.getElementById('date_visite');
    if (dateVisiteField) {
      const today = new Date().toISOString().split('T')[0];
      dateVisiteField.value = today;
      dateVisiteField.min = today;
    }

    // Initialiser l'heure actuelle
    const heureArriveeField = document.getElementById('heure_arrivee');
    if (heureArriveeField) {
      const now = new Date();
      const currentTime = now.getHours().toString().padStart(2, '0') + ':' +
                         now.getMinutes().toString().padStart(2, '0');
      heureArriveeField.value = currentTime;
    }
  }

  setupFormValidation() {
    // Configuration de la validation en temps réel
    const requiredFields = ['nom', 'prenom', 'telephone', 'email', 'entreprise', 'personne_voir', 'motif'];
    
    requiredFields.forEach(fieldId => {
      const field = document.getElementById(fieldId);
      if (field) {
        field.addEventListener('blur', () => this.validateField(field));
        field.addEventListener('input', () => this.clearFieldError(field));
      }
    });
  }

  handleFormInput(event) {
    // Sauvegarde automatique avec debounce
    clearTimeout(this.saveTimeout);
    this.saveTimeout = setTimeout(() => {
      this.saveFormDataToSession();
    }, 1000);
  }

  handleFormSubmit(event) {
    event.preventDefault();
    
    // Vérifier l'authentification avant soumission
    if (!this.checkAuthentication()) {
      return;
    }
    
    // Valider le formulaire
    if (this.validateForm()) {
      this.saveVisitorData();
    }
  }

  handlePrint(event) {
    event.preventDefault();
    
    // Vérifier l'authentification
    if (!this.checkAuthentication()) {
      return;
    }
    
    // Vérifier que le formulaire est valide avant impression
    if (!this.validateForm()) {
      this.showMessage('Veuillez remplir tous les champs obligatoires avant d\'imprimer.', 'warning');
      return;
    }
    
    this.showDevelopmentAlert('Impression');
  }

  handleReturnToHome(event) {
    event.preventDefault();
    
    // Vérifier s'il y a des données non sauvegardées
    const hasUnsavedData = this.checkUnsavedData();
    
    if (hasUnsavedData) {
      const confirmLeave = confirm('Vous avez des données non sauvegardées.\n\nVoulez-vous vraiment quitter sans sauvegarder ?');
      if (!confirmLeave) {
        return;
      }
      
      // Nettoyer les données de session si l'utilisateur confirme
      sessionStorage.removeItem('visitor_form_data');
    }
    
    // Naviguer vers l'accueil
    this.navigateToHome();
  }

  handleDevelopmentFeature(event) {
    event.preventDefault();
    
    const featureName = event.currentTarget.getAttribute('data-dev-feature');
    this.showDevelopmentAlert(featureName);
  }

  validateForm() {
    const requiredFields = ['nom', 'prenom', 'telephone', 'email', 'entreprise', 'personne_voir', 'motif'];
    let isValid = true;
    let firstInvalidField = null;

    requiredFields.forEach(fieldId => {
      const field = document.getElementById(fieldId);
      if (field) {
        const fieldValid = this.validateField(field);
        if (!fieldValid && !firstInvalidField) {
          firstInvalidField = field;
        }
        isValid = isValid && fieldValid;
      }
    });

    if (!isValid) {
      this.showMessage('Veuillez corriger les erreurs dans le formulaire', 'error');
      if (firstInvalidField) {
        firstInvalidField.focus();
      }
    }

    return isValid;
  }

  validateField(field) {
    const value = field.value.trim();
    let isValid = true;
    let errorMessage = '';

    // Validation required
    if (field.hasAttribute('required') && !value) {
      isValid = false;
      errorMessage = 'Ce champ est obligatoire';
    }

    // Validation spécifique par type de champ
    if (isValid && value) {
      switch (field.id) {
        case 'email':
          const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailPattern.test(value)) {
            isValid = false;
            errorMessage = 'Adresse email invalide';
          }
          break;
        case 'telephone':
          const phonePattern = /^[\+]?[0-9\s\-\(\)]{8,15}$/;
          if (!phonePattern.test(value)) {
            isValid = false;
            errorMessage = 'Numéro de téléphone invalide';
          }
          break;
        case 'nom':
        case 'prenom':
          if (value.length < 2) {
            isValid = false;
            errorMessage = 'Minimum 2 caractères';
          }
          break;
      }
    }

    this.updateFieldStatus(field, isValid, errorMessage);
    return isValid;
  }

  updateFieldStatus(field, isValid, errorMessage) {
    // Supprimer les classes d'état précédentes
    field.classList.remove('error', 'success');
    
    // Supprimer le message d'erreur existant
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
      existingError.remove();
    }

    if (!isValid && errorMessage) {
      field.classList.add('error');
      
      // Ajouter le message d'erreur
      const errorDiv = document.createElement('div');
      errorDiv.className = 'field-error';
      errorDiv.textContent = errorMessage;
      field.parentNode.appendChild(errorDiv);
    } else if (field.value.trim()) {
      field.classList.add('success');
    }
  }

  clearFieldError(field) {
    field.classList.remove('error');
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
      existingError.remove();
    }
  }

  saveVisitorData() {
    const form = document.getElementById('visitorForm');
    const formData = new FormData(form);
    const visitorData = {};
    
    formData.forEach((value, key) => {
      visitorData[key] = this.sanitizeString(value);
    });
    
    // Ajouter les métadonnées
    visitorData.id = this.generateId();
    visitorData.created_at = new Date().toISOString();
    visitorData.created_by = this.getCurrentUser();
    
    try {
      // Sauvegarder dans localStorage
      const visitors = JSON.parse(localStorage.getItem('visitors') || '[]');
      visitors.push(visitorData);
      localStorage.setItem('visitors', JSON.stringify(visitors));
      
      // Nettoyer les données de session
      sessionStorage.removeItem('visitor_form_data');
      
      this.showMessage('✅ Visiteur enregistré avec succès !', 'success');
      
      setTimeout(() => {
        this.navigateToHome();
      }, 1500);
      
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      this.showMessage('❌ Erreur lors de la sauvegarde', 'error');
    }
  }

  saveFormDataToSession() {
    const form = document.getElementById('visitorForm');
    if (form) {
      const formData = new FormData(form);
      const dataObject = {};
      
      formData.forEach((value, key) => {
        dataObject[key] = value;
      });
      
      sessionStorage.setItem('visitor_form_data', JSON.stringify(dataObject));
    }
  }

  loadFormData() {
    try {
      const savedData = sessionStorage.getItem('visitor_form_data');
      if (savedData) {
        const formData = JSON.parse(savedData);
        
        Object.keys(formData).forEach(key => {
          const field = document.getElementById(key);
          if (field && formData[key]) {
            field.value = formData[key];
          }
        });
        
        console.log('📋 Données de formulaire restaurées');
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
    }

    // Charger la photo sauvegardée
    if (window.photoManager) {
      window.photoManager.loadSavedPhoto();
    }
  }

  checkUnsavedData() {
    const form = document.getElementById('visitorForm');
    if (!form) return false;
    
    const formData = new FormData(form);
    let hasData = false;
    
    formData.forEach((value, key) => {
      if (value.trim() && key !== 'date_visite' && key !== 'heure_arrivee') {
        hasData = true;
      }
    });
    
    return hasData;
  }

  // ===== MÉTHODES UTILITAIRES =====

  navigateToHome() {
    if (window.navigationManager) {
      window.navigationManager.goToPage('accueil');
    } else {
      window.location.href = 'accueil.html';
    }
  }

  showMessage(message, type) {
    if (window.notificationManager) {
      window.notificationManager.show(message, type);
    } else {
      alert(message);
    }
  }

  showDevelopmentAlert(featureName) {
    const message = `Fonctionnalité en développement : ${featureName}`;
    
    if (window.notificationManager) {
      window.notificationManager.show(`🚧 ${featureName} en développement`, 'warning');
    } else {
      alert(`${message}\n\nCette fonctionnalité sera disponible dans une prochaine version.`);
    }
  }

  getCurrentUser() {
    try {
      const session = localStorage.getItem('user_session');
      if (session) {
        const userData = JSON.parse(session);
        return userData.username;
      }
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'utilisateur:', error);
    }
    return 'Utilisateur inconnu';
  }

  sanitizeString(str) {
    if (typeof str !== 'string') return '';
    return str.replace(/[<>'"&]/g, (char) => {
      const entities = {
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#x27;',
        '&': '&amp;'
      };
      return entities[char];
    });
  }

  generateId() {
    return 'visitor_' + Math.random().toString(36).substring(2, 9) + '_' + Date.now();
  }
}

// ===== FONCTIONS GLOBALES POUR COMPATIBILITÉ =====

window.handlePrint = function() {
  if (window.mainEventManager) {
    window.mainEventManager.handlePrint({ preventDefault: () => {} });
  } else {
    alert('Fonctionnalité d\'impression en développement.\n\nLe formulaire sera imprimé une fois tous les champs remplis.');
  }
};

window.handleReturnToHome = function() {
  if (window.mainEventManager) {
    window.mainEventManager.handleReturnToHome({ preventDefault: () => {} });
  } else {
    if (confirm('Voulez-vous vraiment quitter cette page ?')) {
      window.location.href = 'accueil.html';
    }
  }
};

// ===== INITIALISATION =====
document.addEventListener('DOMContentLoaded', () => {
  // Initialiser le gestionnaire d'événements
  window.mainEventManager = new MainEventManager();
  console.log('📝 Gestionnaire d\'événements formulaire initialisé');
});

// Export pour utilisation dans d'autres modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = MainEventManager;
}
