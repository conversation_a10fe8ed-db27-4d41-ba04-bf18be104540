/**
 * Gestionnaire d'événements pour la page d'accueil
 * Séparation propre du JavaScript selon les bonnes pratiques
 */

'use strict';

// ===== GESTIONNAIRE D'ÉVÉNEMENTS ACCUEIL =====
class AccueilEventManager {
  constructor() {
    this.init();
  }

  init() {
    this.bindEvents();
    this.loadPageData();
  }

  bindEvents() {
    // Événements pour les boutons d'action
    this.bindActionButtons();
    
    // Événements pour la navigation
    this.bindNavigationEvents();
    
    // Événements pour les cartes d'action
    this.bindActionCards();
  }

  bindActionButtons() {
    const newVisitorBtn = document.getElementById('newVisitorBtn');
    if (newVisitorBtn) {
      newVisitorBtn.addEventListener('click', this.handleNewVisitor.bind(this));
    }
  }

  bindNavigationEvents() {
    // Gestion des liens de navigation avec alertes
    const devLinks = document.querySelectorAll('[data-dev-feature]');
    devLinks.forEach(link => {
      link.addEventListener('click', this.handleDevelopmentFeature.bind(this));
    });

    // Gestion de la déconnexion
    const logoutBtn = document.querySelector('[data-action="logout"]');
    if (logoutBtn) {
      logoutBtn.addEventListener('click', this.handleLogout.bind(this));
    }
  }

  bindActionCards() {
    const actionCards = document.querySelectorAll('.action-card');
    actionCards.forEach(card => {
      card.addEventListener('click', this.handleActionCard.bind(this));
    });
  }

  handleNewVisitor(event) {
    event.preventDefault();
    
    // Vérifier l'authentification
    if (window.AuthManager && !window.AuthManager.isAuthenticated()) {
      this.showSessionExpiredAlert();
      return;
    }
    
    // Naviguer vers le formulaire
    this.navigateToForm();
  }

  handleDevelopmentFeature(event) {
    event.preventDefault();
    
    const featureName = event.currentTarget.getAttribute('data-dev-feature');
    this.showDevelopmentAlert(featureName);
  }

  handleLogout(event) {
    event.preventDefault();
    
    if (this.confirmLogout()) {
      this.performLogout();
    }
  }

  handleActionCard(event) {
    event.preventDefault();
    
    const card = event.currentTarget;
    const action = card.getAttribute('data-action');
    
    switch (action) {
      case 'view-visitors':
        this.showDevelopmentAlert('Liste des visiteurs');
        break;
      case 'statistics':
        this.showDevelopmentAlert('Statistiques et rapports');
        break;
      default:
        console.warn('Action non reconnue:', action);
    }
  }

  // ===== MÉTHODES UTILITAIRES =====
  
  navigateToForm() {
    if (window.navigationManager) {
      window.navigationManager.goToPage('formulaire');
    } else {
      window.location.href = 'main.html';
    }
  }

  showSessionExpiredAlert() {
    if (window.notificationManager) {
      window.notificationManager.show('Session expirée. Veuillez vous reconnecter.', 'warning');
    } else {
      alert('Session expirée. Veuillez vous reconnecter.');
    }
    
    setTimeout(() => {
      window.location.href = 'login.html';
    }, 2000);
  }

  showDevelopmentAlert(featureName) {
    const message = `Fonctionnalité en développement : ${featureName}`;
    
    if (window.notificationManager) {
      window.notificationManager.show(`🚧 ${featureName} en développement`, 'warning');
    } else {
      alert(`${message}\n\nCette fonctionnalité sera disponible dans une prochaine version.`);
    }
  }

  confirmLogout() {
    return confirm('Êtes-vous sûr de vouloir vous déconnecter ?');
  }

  performLogout() {
    if (window.AuthManager) {
      window.AuthManager.logout();
    } else {
      localStorage.clear();
      sessionStorage.clear();
      window.location.href = 'login.html';
    }
  }

  loadPageData() {
    this.loadUserData();
    this.loadStatistics();
  }

  loadUserData() {
    try {
      const session = localStorage.getItem('user_session');
      if (session) {
        const userData = JSON.parse(session);
        console.log('👤 Utilisateur connecté:', userData.username);
        
        const userDisplay = document.getElementById('currentUser');
        if (userDisplay) {
          userDisplay.textContent = userData.username;
        }
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données utilisateur:', error);
    }
  }

  loadStatistics() {
    // Simuler le chargement des statistiques avec animation
    setTimeout(() => {
      this.animateStatistics();
    }, 500);
  }

  animateStatistics() {
    const todayVisitors = document.getElementById('todayVisitors');
    const monthlyTotal = document.getElementById('monthlyTotal');
    
    if (todayVisitors) {
      this.animateNumber(todayVisitors, Math.floor(Math.random() * 50) + 1);
    }
    
    if (monthlyTotal) {
      this.animateNumber(monthlyTotal, Math.floor(Math.random() * 500) + 100);
    }
  }

  animateNumber(element, targetValue) {
    const duration = 1000;
    const startValue = 0;
    const startTime = performance.now();
    
    const animate = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // Fonction d'easing
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const currentValue = Math.floor(startValue + (targetValue - startValue) * easeOutQuart);
      
      element.textContent = currentValue;
      
      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };
    
    requestAnimationFrame(animate);
  }
}

// ===== FONCTIONS GLOBALES POUR COMPATIBILITÉ =====

window.goToNewVisitor = function() {
  if (window.accueilEventManager) {
    window.accueilEventManager.handleNewVisitor({ preventDefault: () => {} });
  } else {
    // Fallback
    if (window.AuthManager && !window.AuthManager.isAuthenticated()) {
      alert('Session expirée. Veuillez vous reconnecter.');
      window.location.href = 'login.html';
      return;
    }
    
    if (window.navigationManager) {
      window.navigationManager.goToPage('formulaire');
    } else {
      window.location.href = 'main.html';
    }
  }
};

window.handleLogout = function(event) {
  if (window.accueilEventManager) {
    window.accueilEventManager.handleLogout(event);
  } else {
    // Fallback
    event.preventDefault();
    
    if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
      if (window.AuthManager) {
        window.AuthManager.logout();
      } else {
        localStorage.clear();
        window.location.href = 'login.html';
      }
    }
  }
};

// ===== INITIALISATION =====
document.addEventListener('DOMContentLoaded', () => {
  // Vérifier l'authentification
  if (window.AuthManager && !window.AuthManager.isAuthenticated()) {
    console.log('🔒 Accès non autorisé - Redirection vers login');
    window.location.href = 'login.html';
    return;
  }
  
  // Initialiser le gestionnaire d'événements
  window.accueilEventManager = new AccueilEventManager();
  console.log('🏠 Gestionnaire d\'événements accueil initialisé');
});

// Export pour utilisation dans d'autres modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AccueilEventManager;
}
