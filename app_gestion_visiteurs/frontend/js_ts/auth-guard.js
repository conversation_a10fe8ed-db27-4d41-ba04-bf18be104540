/**
 * Garde d'authentification global - DCOP 413
 * S'assure que la page de connexion est toujours la première page
 * et que l'accès aux autres pages nécessite une authentification
 */

'use strict';

// ===== GARDE D'AUTHENTIFICATION GLOBAL =====
class AuthGuard {
  static PROTECTED_PAGES = [
    'accueil.html',
    'main.html', 
    'photo.html'
  ];

  static PUBLIC_PAGES = [
    'login.html',
    'index.html'
  ];

  static init() {
    console.log('🛡️ Initialisation du garde d\'authentification');
    
    // Vérification immédiate
    AuthGuard.checkPageAccess();
    
    // Surveillance continue
    AuthGuard.setupContinuousMonitoring();
  }

  static checkPageAccess() {
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const isAuthenticated = AuthGuard.isAuthenticated();

    console.log(`🔍 Vérification d'accès pour: ${currentPage} (Auth: ${isAuthenticated})`);

    // Utiliser NavigationConfig si disponible
    if (window.NavigationConfig) {
      return window.NavigationConfig.handleNavigation(isAuthenticated, currentPage);
    }

    // Fallback - logique basique
    if (AuthGuard.isProtectedPage(currentPage)) {
      if (!isAuthenticated) {
        console.log('🔒 Accès refusé - Redirection vers login');
        AuthGuard.redirectToLogin();
        return false;
      } else {
        console.log('✅ Accès autorisé - Utilisateur authentifié');
        return true;
      }
    }

    // Si on est sur une page publique et déjà authentifié
    if (AuthGuard.isPublicPage(currentPage) && isAuthenticated) {
      // Exception pour index.html qui doit toujours rediriger vers login
      if (currentPage === 'index.html') {
        console.log('🔄 Page index - Redirection vers login pour nettoyage');
        AuthGuard.redirectToLogin();
        return false;
      }

      // Pour login.html, rediriger vers accueil si déjà connecté
      if (currentPage === 'login.html') {
        console.log('🏠 Déjà authentifié - Redirection vers accueil');
        AuthGuard.redirectToHome();
        return false;
      }
    }

    return true;
  }

  static isProtectedPage(pageName) {
    return AuthGuard.PROTECTED_PAGES.some(page => pageName.includes(page));
  }

  static isPublicPage(pageName) {
    return AuthGuard.PUBLIC_PAGES.some(page => pageName.includes(page));
  }

  static isAuthenticated() {
    // Utiliser AuthManager si disponible, sinon vérification basique
    if (window.AuthManager && typeof window.AuthManager.isAuthenticated === 'function') {
      return window.AuthManager.isAuthenticated();
    }
    
    // Vérification basique en fallback
    try {
      const token = localStorage.getItem('dcop413_auth_token');
      const session = localStorage.getItem('dcop413_user_session');
      
      if (!token || !session) {
        return false;
      }
      
      const sessionData = JSON.parse(session);
      const now = new Date();
      const expiresAt = new Date(sessionData.expiresAt);
      
      return sessionData.isAuthenticated && now < expiresAt;
    } catch (error) {
      console.error('Erreur vérification authentification:', error);
      return false;
    }
  }

  static redirectToLogin() {
    // Nettoyer la session avant redirection
    AuthGuard.clearSession();
    
    // Redirection sécurisée
    if (window.location.pathname.includes('login.html')) {
      return; // Déjà sur login
    }
    
    console.log('🔄 Redirection vers login.html');
    window.location.href = 'login.html';
  }

  static redirectToHome() {
    if (window.location.pathname.includes('accueil.html')) {
      return; // Déjà sur accueil
    }
    
    console.log('🏠 Redirection vers accueil.html');
    window.location.href = 'accueil.html';
  }

  static clearSession() {
    try {
      localStorage.removeItem('dcop413_auth_token');
      localStorage.removeItem('dcop413_user_session');
      localStorage.removeItem('dcop413_redirect_after_login');
      sessionStorage.clear();
    } catch (error) {
      console.error('Erreur nettoyage session:', error);
    }
  }

  static setupContinuousMonitoring() {
    // Surveillance des changements de visibilité
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        AuthGuard.checkPageAccess();
      }
    });

    // Surveillance des changements de focus
    window.addEventListener('focus', () => {
      AuthGuard.checkPageAccess();
    });

    // Surveillance des changements de stockage (déconnexion dans autre onglet)
    window.addEventListener('storage', (e) => {
      if (e.key === 'dcop413_auth_token' && !e.newValue) {
        console.log('🔒 Déconnexion détectée dans un autre onglet');
        AuthGuard.redirectToLogin();
      }
    });

    // Vérification périodique
    setInterval(() => {
      const currentPage = window.location.pathname.split('/').pop() || 'index.html';
      if (AuthGuard.isProtectedPage(currentPage) && !AuthGuard.isAuthenticated()) {
        console.log('🔒 Session expirée détectée - Redirection');
        AuthGuard.redirectToLogin();
      }
    }, 30000); // Vérification toutes les 30 secondes
  }

  // Méthode pour forcer une vérification manuelle
  static forceCheck() {
    return AuthGuard.checkPageAccess();
  }

  // Méthode pour obtenir l'état d'authentification
  static getAuthStatus() {
    return {
      isAuthenticated: AuthGuard.isAuthenticated(),
      currentPage: window.location.pathname.split('/').pop() || 'index.html',
      isProtectedPage: AuthGuard.isProtectedPage(window.location.pathname.split('/').pop() || 'index.html')
    };
  }
}

// ===== INITIALISATION AUTOMATIQUE =====
// Exécution immédiate avant même DOMContentLoaded
(function() {
  console.log('🚀 Démarrage du garde d\'authentification');
  
  // Vérification immédiate
  if (!AuthGuard.checkPageAccess()) {
    return; // Arrêter si redirection nécessaire
  }
  
  // Initialisation complète quand le DOM est prêt
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      AuthGuard.init();
    });
  } else {
    AuthGuard.init();
  }
})();

// Exposition globale pour utilisation externe
window.AuthGuard = AuthGuard;

// Export pour utilisation dans d'autres modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AuthGuard;
}
