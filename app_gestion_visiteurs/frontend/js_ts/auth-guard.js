/**
 * Garde d'authentification global - DCOP 413
 * S'assure que la page de connexion est toujours la première page
 * et que l'accès aux autres pages nécessite une authentification
 */

'use strict';

// ===== GARDE D'AUTHENTIFICATION GLOBAL =====
class AuthGuard {
  static PROTECTED_ROUTES = [
    '/accueil',
    '/formulaire',
    '/photo'
  ];

  static PUBLIC_ROUTES = [
    '/login',
    '/'
  ];

  static init() {
    console.log('🛡️ Initialisation du garde d\'authentification');

    // Vérification immédiate (seulement si pas déjà en cours)
    if (!AuthGuard._initializing) {
      AuthGuard._initializing = true;
      AuthGuard.checkPageAccess();

      // Surveillance continue (désactivée pour éviter les boucles)
      // AuthGuard.setupContinuousMonitoring();
    }
  }

  static checkPageAccess() {
    const currentRoute = window.location.pathname;
    const isAuthenticated = AuthGuard.isAuthenticated();

    console.log(`🔍 Vérification d'accès pour: ${currentRoute} (Auth: ${isAuthenticated})`);

    // Utiliser NavigationConfig si disponible
    if (window.NavigationConfig) {
      return window.NavigationConfig.handleNavigation(isAuthenticated, currentRoute);
    }

    // Fallback - logique basique
    if (AuthGuard.isProtectedRoute(currentRoute)) {
      if (!isAuthenticated) {
        console.log('🔒 Accès refusé - Redirection vers login');
        AuthGuard.redirectToLogin();
        return false;
      } else {
        console.log('✅ Accès autorisé - Utilisateur authentifié');
        return true;
      }
    }

    // Si on est sur une route publique et déjà authentifié
    if (AuthGuard.isPublicRoute(currentRoute) && isAuthenticated) {
      // Exception pour la route racine qui doit toujours rediriger vers login
      if (currentRoute === '/') {
        console.log('🔄 Route racine - Redirection vers login pour nettoyage');
        AuthGuard.redirectToLogin();
        return false;
      }

      // Pour /login, rediriger vers accueil si déjà connecté (MAIS SEULEMENT UNE FOIS)
      if (currentRoute === '/login' && !AuthGuard._redirecting) {
        console.log('🏠 Déjà authentifié - Redirection vers accueil');
        AuthGuard._redirecting = true; // Éviter les boucles
        setTimeout(() => {
          AuthGuard.redirectToHome();
        }, 100);
        return false;
      }
    }

    return true;
  }

  static isProtectedRoute(route) {
    return AuthGuard.PROTECTED_ROUTES.includes(route);
  }

  static isPublicRoute(route) {
    return AuthGuard.PUBLIC_ROUTES.includes(route);
  }

  static isAuthenticated() {
    // Utiliser AuthManager si disponible, sinon vérification basique
    if (window.AuthManager && typeof window.AuthManager.isAuthenticated === 'function') {
      return window.AuthManager.isAuthenticated();
    }
    
    // Vérification basique en fallback
    try {
      const token = localStorage.getItem('dcop413_auth_token');
      const session = localStorage.getItem('dcop413_user_session');
      
      if (!token || !session) {
        return false;
      }
      
      const sessionData = JSON.parse(session);
      const now = new Date();
      const expiresAt = new Date(sessionData.expiresAt);
      
      return sessionData.isAuthenticated && now < expiresAt;
    } catch (error) {
      console.error('Erreur vérification authentification:', error);
      return false;
    }
  }

  static redirectToLogin() {
    // Nettoyer la session avant redirection
    AuthGuard.clearSession();
    
    // Redirection sécurisée
    if (window.location.pathname.includes('login.html')) {
      return; // Déjà sur login
    }
    
    console.log('🔄 Redirection vers /login');
    window.location.href = '/login';
  }

  static redirectToHome() {
    if (window.location.pathname.includes('accueil.html')) {
      return; // Déjà sur accueil
    }
    
    console.log('🏠 Redirection vers /accueil');
    window.location.href = '/accueil';
  }

  static clearSession() {
    try {
      localStorage.removeItem('dcop413_auth_token');
      localStorage.removeItem('dcop413_user_session');
      localStorage.removeItem('dcop413_redirect_after_login');
      sessionStorage.clear();
    } catch (error) {
      console.error('Erreur nettoyage session:', error);
    }
  }

  static setupContinuousMonitoring() {
    // Surveillance des changements de visibilité
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        AuthGuard.checkPageAccess();
      }
    });

    // Surveillance des changements de focus
    window.addEventListener('focus', () => {
      AuthGuard.checkPageAccess();
    });

    // Surveillance des changements de stockage (déconnexion dans autre onglet)
    window.addEventListener('storage', (e) => {
      if (e.key === 'dcop413_auth_token' && !e.newValue) {
        console.log('🔒 Déconnexion détectée dans un autre onglet');
        AuthGuard.redirectToLogin();
      }
    });

    // Vérification périodique (désactivée pour éviter les boucles)
    /*
    setInterval(() => {
      const currentRoute = window.location.pathname;
      if (AuthGuard.isProtectedRoute(currentRoute) && !AuthGuard.isAuthenticated()) {
        console.log('🔒 Session expirée détectée - Redirection');
        AuthGuard.redirectToLogin();
      }
    }, 30000); // Vérification toutes les 30 secondes
    */
  }

  // Méthode pour forcer une vérification manuelle
  static forceCheck() {
    return AuthGuard.checkPageAccess();
  }

  // Méthode pour obtenir l'état d'authentification
  static getAuthStatus() {
    return {
      isAuthenticated: AuthGuard.isAuthenticated(),
      currentPage: window.location.pathname.split('/').pop() || 'index.html',
      isProtectedPage: AuthGuard.isProtectedPage(window.location.pathname.split('/').pop() || 'index.html')
    };
  }
}

// ===== INITIALISATION AUTOMATIQUE =====
// Exécution immédiate avant même DOMContentLoaded
(function() {
  console.log('🚀 Démarrage du garde d\'authentification');
  
  // Vérification immédiate
  if (!AuthGuard.checkPageAccess()) {
    return; // Arrêter si redirection nécessaire
  }
  
  // Initialisation complète quand le DOM est prêt
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      AuthGuard.init();
    });
  } else {
    AuthGuard.init();
  }
})();

// Exposition globale pour utilisation externe
window.AuthGuard = AuthGuard;

// Export pour utilisation dans d'autres modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AuthGuard;
}
