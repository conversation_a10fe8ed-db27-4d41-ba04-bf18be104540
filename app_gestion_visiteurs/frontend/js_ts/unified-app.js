/**
 * Application JavaScript Unifiée - DCOP 413
 * Consolidation de toutes les fonctionnalités principales
 * Version optimisée et modulaire
 */

'use strict';

// ===== CONFIGURATION GLOBALE =====
const APP_CONFIG = {
  security: {
    MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
    ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
    MAX_IMAGE_DIMENSION: 2048,
    API_TIMEOUT: 30000
  },
  animations: {
    duration: 300,
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
  },
  delays: {
    notification: 3000,
    redirect: 800
  }
};

// ===== UTILITAIRES GÉNÉRAUX =====
const Utils = {
  $(selector) {
    return document.querySelector(selector);
  },
  
  $$(selector) {
    return document.querySelectorAll(selector);
  },
  
  sanitizeString(str) {
    if (typeof str !== 'string') return '';
    return str.replace(/[<>'"&]/g, (char) => {
      const entities = {
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#x27;',
        '&': '&amp;'
      };
      return entities[char];
    });
  },
  
  generateId() {
    return 'app_' + Math.random().toString(36).substring(2, 9) + '_' + Date.now();
  },
  
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },
  
  formatDate(date) {
    return new Intl.DateTimeFormat('fr-FR').format(new Date(date));
  },
  
  formatTime(date) {
    return new Intl.DateTimeFormat('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date));
  }
};

// ===== GESTIONNAIRE DE NOTIFICATIONS =====
class NotificationManager {
  constructor() {
    this.container = null;
    this.init();
  }
  
  init() {
    this.container = document.createElement('div');
    this.container.className = 'notification-container';
    document.body.appendChild(this.container);
  }
  
  show(message, type = 'info', duration = APP_CONFIG.delays.notification) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    this.container.appendChild(notification);

    // Animation d'entrée
    setTimeout(() => {
      notification.classList.add('show');
    }, 10);

    // Suppression automatique
    setTimeout(() => {
      this.remove(notification);
    }, duration);

    // Suppression au clic
    notification.addEventListener('click', () => {
      this.remove(notification);
    });
  }
  
  remove(notification) {
    notification.classList.remove('show');
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }
  

}

// ===== GESTIONNAIRE D'AUTHENTIFICATION =====
class AuthManager {
  static async login(username, password) {
    try {
      if (window.notificationManager) {
        window.notificationManager.show('🔄 Connexion en cours...', 'info');
      }

      if (username && password) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const sessionData = {
          token: "demo_token_" + Date.now(),
          username: username,
          loginTime: new Date().toISOString(),
          isAuthenticated: true
        };
        
        localStorage.setItem("auth_token", sessionData.token);
        localStorage.setItem("user_session", JSON.stringify(sessionData));
        
        if (window.notificationManager) {
          window.notificationManager.show('✅ Connexion réussie !', 'success');
        }
        
        const redirectUrl = AuthManager.getRedirectUrl() || 'accueil.html';
        
        setTimeout(() => {
          if (window.navigationManager) {
            window.navigationManager.goToPage('accueil');
          } else {
            window.location.href = redirectUrl;
          }
        }, APP_CONFIG.delays.redirect);
        
        return true;
      } else {
        throw new Error('Identifiants manquants');
      }
      
    } catch (error) {
      console.error("Erreur lors de la connexion:", error);
      
      if (window.notificationManager) {
        window.notificationManager.show('❌ Erreur de connexion', 'error');
      }
      
      return false;
    }
  }

  static async logout() {
    try {
      if (window.notificationManager) {
        window.notificationManager.show('🔄 Déconnexion en cours...', 'info');
      }

      localStorage.removeItem("auth_token");
      localStorage.removeItem("user_session");
      sessionStorage.clear();

      if (window.notificationManager) {
        window.notificationManager.show('✅ Déconnexion réussie !', 'success');
      }

      setTimeout(() => {
        window.location.href = "login.html";
      }, APP_CONFIG.delays.redirect);

    } catch (error) {
      console.error("Erreur lors de la déconnexion:", error);
      localStorage.removeItem("auth_token");
      localStorage.removeItem("user_session");
      sessionStorage.clear();

      if (window.notificationManager) {
        window.notificationManager.show('⚠️ Déconnexion forcée', 'warning');
      }

      setTimeout(() => {
        window.location.href = "login.html";
      }, 1000);
    }
  }

  static getRedirectUrl() {
    const redirectUrl = localStorage.getItem('redirect_after_login');
    localStorage.removeItem('redirect_after_login');
    return redirectUrl;
  }

  static setRedirectUrl(url) {
    localStorage.setItem('redirect_after_login', url);
  }

  static isAuthenticated() {
    const token = localStorage.getItem("auth_token");
    const session = localStorage.getItem("user_session");
    
    if (!token || !session) {
      return false;
    }
    
    try {
      const sessionData = JSON.parse(session);
      return sessionData.isAuthenticated && sessionData.token === token;
    } catch (error) {
      console.error("Erreur de validation de session:", error);
      return false;
    }
  }

  static requireAuth() {
    if (!AuthManager.isAuthenticated()) {
      AuthManager.setRedirectUrl(window.location.href);
      
      if (window.navigationManager) {
        window.navigationManager.goToPage('login');
      } else {
        window.location.href = 'login.html';
      }
      
      return false;
    }
    
    return true;
  }
}

// ===== GESTIONNAIRE DE NAVIGATION =====
class NavigationManager {
  constructor() {
    this.currentPage = this.getCurrentPage();
    this.setupNavigationEvents();
  }

  getCurrentPage() {
    const path = window.location.pathname;
    const filename = path.split('/').pop() || 'index.html';
    
    const pageMap = {
      'index.html': 'accueil',
      'accueil.html': 'accueil',
      'login.html': 'login',
      'main.html': 'formulaire',
      'photo.html': 'photo',
      '': 'accueil'
    };
    
    return pageMap[filename] || 'accueil';
  }

  setupNavigationEvents() {
    document.addEventListener('click', (e) => {
      const link = e.target.closest('a[href]');
      if (link && this.isInternalLink(link)) {
        e.preventDefault();
        this.navigateToPage(link.href, link);
      }
    });

    window.addEventListener('popstate', (e) => {
      if (e.state && e.state.page) {
        this.handleBackNavigation(e.state.page);
      }
    });
  }

  isInternalLink(link) {
    const href = link.getAttribute('href');
    
    if (!href || 
        href.startsWith('http') || 
        href.startsWith('mailto:') || 
        href.startsWith('tel:') || 
        href.startsWith('#') ||
        href === 'javascript:void(0)') {
      return false;
    }
    
    return true;
  }

  navigateToPage(url, element = null) {
    if (element) {
      this.addTransitionEffect(element);
    }

    setTimeout(() => {
      window.location.href = url;
    }, APP_CONFIG.animations.duration);
  }

  addTransitionEffect(element) {
    element.classList.add('element-transition', 'scale-down');
  }

  goToPage(pageName) {
    const pageUrls = {
      'accueil': 'accueil.html',
      'login': 'login.html',
      'formulaire': 'main.html',
      'main': 'main.html',
      'photo': 'photo.html'
    };
    
    const url = pageUrls[pageName];
    if (url) {
      this.navigateToPage(url);
    } else {
      console.warn(`Page inconnue: ${pageName}`);
    }
  }

  static navigateTo(page) {
    if (window.navigationManager) {
      window.navigationManager.goToPage(page);
    } else {
      const pageUrls = {
        'accueil': 'accueil.html',
        'login': 'login.html',
        'formulaire': 'main.html',
        'main': 'main.html',
        'photo': 'photo.html'
      };
      
      const url = pageUrls[page];
      if (url) {
        window.location.href = url;
      }
    }
  }

  validateAndNavigate(url) {
    if (!url || url === '#') {
      console.warn('URL invalide ou vide');
      return false;
    }

    if (url.startsWith('http') || url.startsWith('mailto:') || url.startsWith('tel:')) {
      window.open(url, '_blank');
      return true;
    }

    this.navigateToPage(url);
    return true;
  }
}

// ===== GESTIONNAIRE DE FORMULAIRES =====
class FormManager {
  constructor() {
    this.forms = new Map();
    this.validators = new Map();
    this.init();
  }

  init() {
    this.setupFormValidation();
    this.setupAutoSave();
  }

  setupFormValidation() {
    const forms = Utils.$$('form');
    forms.forEach(form => {
      this.registerForm(form);
    });
  }

  registerForm(form) {
    if (!form.id) return;

    this.forms.set(form.id, form);

    // Validation en temps réel
    form.addEventListener('input', (e) => {
      this.validateField(e.target);
    });

    // Validation à la soumission
    form.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleSubmit(form);
    });
  }

  validateField(field) {
    const rules = this.getValidationRules(field);
    if (!rules) return true;

    const value = field.value.trim();
    let isValid = true;
    let errorMessage = '';

    // Validation required
    if (rules.required && !value) {
      isValid = false;
      errorMessage = 'Ce champ est obligatoire';
    }

    // Validation pattern
    if (isValid && rules.pattern && value && !rules.pattern.test(value)) {
      isValid = false;
      errorMessage = rules.message || 'Format invalide';
    }

    // Validation longueur
    if (isValid && rules.minLength && value.length < rules.minLength) {
      isValid = false;
      errorMessage = `Minimum ${rules.minLength} caractères`;
    }

    if (isValid && rules.maxLength && value.length > rules.maxLength) {
      isValid = false;
      errorMessage = `Maximum ${rules.maxLength} caractères`;
    }

    this.updateFieldStatus(field, isValid, errorMessage);
    return isValid;
  }

  getValidationRules(field) {
    const rules = {
      nom: {
        required: true,
        minLength: 2,
        maxLength: 50,
        pattern: /^[A-Za-zÀ-ÿ\s\-']{2,50}$/,
        message: 'Le nom doit contenir 2-50 caractères (lettres uniquement)'
      },
      prenom: {
        required: true,
        minLength: 2,
        maxLength: 50,
        pattern: /^[A-Za-zÀ-ÿ\s\-']{2,50}$/,
        message: 'Le prénom doit contenir 2-50 caractères (lettres uniquement)'
      },
      email: {
        required: true,
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        message: 'Adresse email invalide'
      },
      telephone: {
        required: true,
        pattern: /^[\+]?[0-9\s\-\(\)]{8,15}$/,
        message: 'Numéro de téléphone invalide'
      }
    };

    return rules[field.name] || rules[field.id];
  }

  updateFieldStatus(field, isValid, errorMessage) {
    // Supprimer les classes d'état précédentes
    field.classList.remove('error', 'success');

    // Supprimer le message d'erreur existant
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
      existingError.remove();
    }

    if (!isValid && errorMessage) {
      field.classList.add('error');

      // Ajouter le message d'erreur
      const errorDiv = document.createElement('div');
      errorDiv.className = 'field-error';
      errorDiv.textContent = errorMessage;
      field.parentNode.appendChild(errorDiv);
    } else if (field.value.trim()) {
      field.classList.add('success');
    }
  }

  validateForm(form) {
    const fields = form.querySelectorAll('input, select, textarea');
    let isValid = true;
    let firstInvalidField = null;

    fields.forEach(field => {
      const fieldValid = this.validateField(field);
      if (!fieldValid && !firstInvalidField) {
        firstInvalidField = field;
      }
      isValid = isValid && fieldValid;
    });

    if (!isValid && firstInvalidField) {
      firstInvalidField.focus();
    }

    return isValid;
  }

  handleSubmit(form) {
    if (!this.validateForm(form)) {
      if (window.notificationManager) {
        window.notificationManager.show('Veuillez corriger les erreurs dans le formulaire', 'error');
      }
      return false;
    }

    // Traitement spécifique selon le type de formulaire
    if (form.id === 'loginForm') {
      this.handleLogin(form);
    } else if (form.id === 'visitorForm') {
      this.handleVisitorSubmission(form);
    }

    return true;
  }

  handleLogin(form) {
    const formData = new FormData(form);
    const username = formData.get('username');
    const password = formData.get('password');

    AuthManager.login(username, password);
  }

  handleVisitorSubmission(form) {
    const formData = new FormData(form);
    const visitorData = {};

    formData.forEach((value, key) => {
      visitorData[key] = Utils.sanitizeString(value);
    });

    // Ajouter les métadonnées
    visitorData.id = Utils.generateId();
    visitorData.created_at = new Date().toISOString();
    visitorData.created_by = this.getCurrentUser();

    // Sauvegarder
    this.saveVisitor(visitorData);
  }

  saveVisitor(visitorData) {
    try {
      const visitors = JSON.parse(localStorage.getItem('visitors') || '[]');
      visitors.push(visitorData);
      localStorage.setItem('visitors', JSON.stringify(visitors));

      // Nettoyer les données de session
      sessionStorage.removeItem('visitor_form_data');

      if (window.notificationManager) {
        window.notificationManager.show('✅ Visiteur enregistré avec succès !', 'success');
      }

      setTimeout(() => {
        if (window.navigationManager) {
          window.navigationManager.goToPage('accueil');
        } else {
          window.location.href = 'accueil.html';
        }
      }, 1500);

    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      if (window.notificationManager) {
        window.notificationManager.show('❌ Erreur lors de la sauvegarde', 'error');
      }
    }
  }

  getCurrentUser() {
    try {
      const session = localStorage.getItem('user_session');
      if (session) {
        const userData = JSON.parse(session);
        return userData.username;
      }
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'utilisateur:', error);
    }
    return 'Utilisateur inconnu';
  }

  setupAutoSave() {
    const visitorForm = Utils.$('#visitorForm');
    if (visitorForm) {
      const debouncedSave = Utils.debounce(() => {
        this.saveFormDataToSession(visitorForm);
      }, 1000);

      visitorForm.addEventListener('input', debouncedSave);
    }
  }

  saveFormDataToSession(form) {
    const formData = new FormData(form);
    const dataObject = {};

    formData.forEach((value, key) => {
      dataObject[key] = value;
    });

    sessionStorage.setItem('visitor_form_data', JSON.stringify(dataObject));
  }

  loadFormDataFromSession(form) {
    try {
      const savedData = sessionStorage.getItem('visitor_form_data');
      if (savedData) {
        const formData = JSON.parse(savedData);

        Object.keys(formData).forEach(key => {
          const field = form.querySelector(`[name="${key}"], #${key}`);
          if (field && formData[key]) {
            field.value = formData[key];
          }
        });

        console.log('📋 Données de formulaire restaurées');
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
    }
  }
}

// ===== GESTIONNAIRE DE PHOTOS =====
class PhotoManager {
  constructor() {
    this.currentPhoto = null;
    this.maxFileSize = APP_CONFIG.security.MAX_FILE_SIZE;
    this.allowedFormats = APP_CONFIG.security.ALLOWED_IMAGE_TYPES;
    this.initializeElements();
    this.bindEvents();
  }

  initializeElements() {
    this.photoContainer = Utils.$('#headerPhotoContainer');
    this.photoPlaceholder = Utils.$('#photoPlaceholder');
    this.photoPreview = Utils.$('#visitorPhoto');
    this.fileInput = Utils.$('#photoFileInput');
    this.uploadBtn = Utils.$('#uploadPhotoBtn');
    this.captureBtn = Utils.$('#capturePhotoBtn');
    this.removeBtn = Utils.$('#removePhotoBtn');
  }

  bindEvents() {
    if (this.fileInput) {
      this.fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
    }

    if (this.uploadBtn) {
      this.uploadBtn.addEventListener('click', () => this.openFileDialog());
    }

    if (this.captureBtn) {
      this.captureBtn.addEventListener('click', () => this.openCamera());
    }

    if (this.removeBtn) {
      this.removeBtn.addEventListener('click', () => this.removePhoto());
    }

    // Écouter les messages de la page photo
    window.addEventListener('message', (e) => {
      if (e.data.type === 'photo_captured') {
        this.handlePhotoCaptured(e.data.photoData);
      }
    });
  }

  openFileDialog() {
    if (this.fileInput) {
      this.fileInput.click();
    }
  }

  handleFileSelect(event) {
    const file = event.target.files[0];
    if (!file) return;

    if (!this.validateFile(file)) {
      return;
    }

    this.processFile(file);
  }

  validateFile(file) {
    if (!this.allowedFormats.includes(file.type)) {
      if (window.notificationManager) {
        window.notificationManager.show('Format de fichier non supporté', 'error');
      }
      return false;
    }

    if (file.size > this.maxFileSize) {
      if (window.notificationManager) {
        window.notificationManager.show('Fichier trop volumineux (max 5MB)', 'error');
      }
      return false;
    }

    return true;
  }

  processFile(file) {
    const reader = new FileReader();

    reader.onload = (e) => {
      this.setPhoto(e.target.result);
    };

    reader.onerror = () => {
      if (window.notificationManager) {
        window.notificationManager.show('Erreur lors de la lecture du fichier', 'error');
      }
    };

    reader.readAsDataURL(file);
  }

  setPhoto(photoData) {
    this.currentPhoto = photoData;

    // Mettre à jour l'affichage
    if (this.photoPreview) {
      this.photoPreview.src = photoData;
      this.photoPreview.classList.remove('photo-preview-hidden');
      this.photoPreview.classList.add('photo-preview-visible');
    }

    if (this.photoPlaceholder) {
      this.photoPlaceholder.classList.remove('photo-placeholder-visible');
      this.photoPlaceholder.classList.add('photo-placeholder-hidden');
    }

    // Sauvegarder en session
    sessionStorage.setItem('visitor_photo', photoData);

    if (window.notificationManager) {
      window.notificationManager.show('📷 Photo ajoutée avec succès', 'success');
    }
  }

  openCamera() {
    // Ouvrir la page photo dans une nouvelle fenêtre
    const photoWindow = window.open('photo.html', 'photoCapture', 'width=600,height=500');

    if (!photoWindow) {
      // Fallback : navigation normale
      if (window.navigationManager) {
        window.navigationManager.goToPage('photo');
      } else {
        window.location.href = 'photo.html';
      }
    }
  }

  handlePhotoCaptured(photoData) {
    this.setPhoto(photoData);
  }

  removePhoto() {
    this.currentPhoto = null;

    if (this.photoPreview) {
      this.photoPreview.src = '';
      this.photoPreview.classList.remove('photo-preview-visible');
      this.photoPreview.classList.add('photo-preview-hidden');
    }

    if (this.photoPlaceholder) {
      this.photoPlaceholder.classList.remove('photo-placeholder-hidden');
      this.photoPlaceholder.classList.add('photo-placeholder-visible');
    }

    if (this.fileInput) {
      this.fileInput.value = '';
    }

    sessionStorage.removeItem('visitor_photo');

    if (window.notificationManager) {
      window.notificationManager.show('Photo supprimée', 'info');
    }
  }

  loadSavedPhoto() {
    const savedPhoto = sessionStorage.getItem('visitor_photo');
    if (savedPhoto) {
      this.setPhoto(savedPhoto);
    }
  }
}

// ===== GESTIONNAIRE DE CAMÉRA (pour page photo) =====
class CameraManager {
  constructor() {
    this.stream = null;
    this.video = Utils.$('#video');
    this.canvas = Utils.$('#canvas');
    this.captureBtn = Utils.$('#captureBtn');
    this.init();
  }

  async init() {
    if (!this.video || !this.canvas) return;

    try {
      this.stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'user' },
        audio: false
      });

      this.video.srcObject = this.stream;
      this.setupCaptureButton();

      this.updateStatus('Caméra prête', 'success');

    } catch (error) {
      console.error('Erreur d\'accès à la caméra:', error);
      this.updateStatus('Erreur d\'accès à la caméra', 'error');
    }
  }

  setupCaptureButton() {
    if (this.captureBtn) {
      this.captureBtn.addEventListener('click', () => this.capturePhoto());
    }
  }

  capturePhoto() {
    if (!this.video || !this.canvas) return;

    const context = this.canvas.getContext('2d');
    this.canvas.width = this.video.videoWidth;
    this.canvas.height = this.video.videoHeight;

    context.drawImage(this.video, 0, 0);

    const photoData = this.canvas.toDataURL('image/jpeg', 0.8);

    // Afficher la prévisualisation
    this.showPreview(photoData);

    // Arrêter la caméra
    this.stopCamera();
  }

  showPreview(photoData) {
    const capturedPhoto = Utils.$('#capturedPhoto');
    const photoContainer = Utils.$('#photoContainer');

    if (capturedPhoto) {
      capturedPhoto.src = photoData;
    }

    if (photoContainer) {
      photoContainer.classList.remove('hidden');
    }

    // Ajouter les boutons d'action
    this.addPreviewActions(photoData);
  }

  addPreviewActions(photoData) {
    const actionsContainer = Utils.$('#photoActions') || this.createActionsContainer();

    // Créer les boutons de manière propre
    actionsContainer.innerHTML = '';

    const useBtn = document.createElement('button');
    useBtn.className = 'ultra-btn ultra-btn-submit';
    useBtn.textContent = '✅ Utiliser cette photo';
    useBtn.addEventListener('click', () => this.usePhoto(photoData));

    const retakeBtn = document.createElement('button');
    retakeBtn.className = 'ultra-btn ultra-btn-cancel';
    retakeBtn.textContent = '🔄 Reprendre';
    retakeBtn.addEventListener('click', () => this.retakePhoto());

    actionsContainer.appendChild(useBtn);
    actionsContainer.appendChild(retakeBtn);
  }

  createActionsContainer() {
    const container = document.createElement('div');
    container.id = 'photoActions';
    container.className = 'photo-actions-container';

    const photoContainer = Utils.$('#photoContainer');
    if (photoContainer) {
      photoContainer.appendChild(container);
    }

    return container;
  }

  usePhoto(photoData) {
    // Envoyer la photo à la fenêtre parent si c'est une popup
    if (window.opener) {
      window.opener.postMessage({
        type: 'photo_captured',
        photoData: photoData
      }, '*');
      window.close();
    } else {
      // Sauvegarder et naviguer
      sessionStorage.setItem('visitor_photo', photoData);

      if (window.notificationManager) {
        window.notificationManager.show('✅ Photo sauvegardée !', 'success');
      }

      setTimeout(() => {
        if (window.navigationManager) {
          window.navigationManager.goToPage('formulaire');
        } else {
          window.location.href = 'main.html';
        }
      }, 1000);
    }
  }

  retakePhoto() {
    const photoContainer = Utils.$('#photoContainer');
    if (photoContainer) {
      photoContainer.classList.add('hidden');
    }

    // Redémarrer la caméra
    this.init();
  }

  stopCamera() {
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }
  }

  updateStatus(message, type) {
    const statusText = Utils.$('#statusText');
    const statusIndicator = Utils.$('#statusIndicator');

    if (statusText) {
      statusText.textContent = message;
    }

    if (statusIndicator) {
      // Supprimer les classes de statut précédentes
      statusIndicator.classList.remove('success', 'error', 'warning', 'info');
      // Ajouter la nouvelle classe de statut
      statusIndicator.classList.add('status-indicator', type || 'info');
    }
  }
}

// ===== FONCTIONS GLOBALES =====
window.navigateTo = function(page) {
  NavigationManager.navigateTo(page);
};

window.goToPage = function(page) {
  if (window.navigationManager) {
    window.navigationManager.goToPage(page);
  } else {
    NavigationManager.navigateTo(page);
  }
};

window.handleLinkClick = function(event, url, confirmMessage = null) {
  event.preventDefault();

  if (confirmMessage && !confirm(confirmMessage)) {
    return false;
  }

  if (window.navigationManager) {
    window.navigationManager.validateAndNavigate(url);
  } else {
    window.location.href = url;
  }

  return true;
};

window.showDevelopmentAlert = function(featureName) {
  const message = `Fonctionnalité en développement : ${featureName}\n\nCette fonctionnalité sera disponible dans une prochaine version.`;

  if (window.notificationManager) {
    window.notificationManager.show(`🚧 ${featureName} en développement`, 'warning');
  } else {
    alert(message);
  }
};

// ===== FONCTIONS SPÉCIALISÉES PAR PAGE =====

// Fonctions pour la page d'accueil
window.goToNewVisitor = function() {
  if (window.AuthManager && !window.AuthManager.isAuthenticated()) {
    alert('Session expirée. Veuillez vous reconnecter.');
    window.location.href = 'login.html';
    return;
  }

  if (window.navigationManager) {
    window.navigationManager.goToPage('formulaire');
  } else {
    window.location.href = 'main.html';
  }
};

window.handleLogout = function(event) {
  event.preventDefault();

  if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
    if (window.AuthManager) {
      window.AuthManager.logout();
    } else {
      localStorage.clear();
      window.location.href = 'login.html';
    }
  }
};

// Fonctions pour la page formulaire
window.handlePrint = function() {
  if (window.AuthManager && !window.AuthManager.isAuthenticated()) {
    alert('Session expirée. Veuillez vous reconnecter.');
    window.location.href = 'login.html';
    return;
  }

  const form = Utils.$('#visitorForm');
  if (form && window.formManager) {
    if (!window.formManager.validateForm(form)) {
      alert('Veuillez remplir tous les champs obligatoires avant d\'imprimer.');
      return;
    }
  }

  if (window.notificationManager) {
    window.notificationManager.show('🖨️ Fonctionnalité d\'impression en développement', 'warning');
  } else {
    alert('Fonctionnalité d\'impression en développement.\n\nLe formulaire sera imprimé une fois tous les champs remplis.');
  }
};

window.handleReturnToHome = function() {
  const hasUnsavedData = checkUnsavedData();

  if (hasUnsavedData) {
    const confirmLeave = confirm('Vous avez des données non sauvegardées.\n\nVoulez-vous vraiment quitter sans sauvegarder ?');
    if (!confirmLeave) {
      return;
    }

    sessionStorage.removeItem('visitor_form_data');
  }

  if (window.navigationManager) {
    window.navigationManager.goToPage('accueil');
  } else {
    window.location.href = 'accueil.html';
  }
};

function checkUnsavedData() {
  const form = Utils.$('#visitorForm');
  if (!form) return false;

  const formData = new FormData(form);
  let hasData = false;

  formData.forEach((value, key) => {
    if (value.trim() && key !== 'date_visite' && key !== 'heure_arrivee') {
      hasData = true;
    }
  });

  return hasData;
}

// Fonctions pour la page photo
window.handleCancel = function() {
  const referrer = document.referrer;

  if (referrer && referrer.includes('main.html')) {
    if (window.navigationManager) {
      window.navigationManager.goToPage('formulaire');
    } else {
      window.location.href = 'main.html';
    }
  } else if (window.opener) {
    window.close();
  } else {
    if (window.navigationManager) {
      window.navigationManager.goToPage('accueil');
    } else {
      window.location.href = 'accueil.html';
    }
  }
};

// ===== INITIALISATION DE L'APPLICATION =====
class AppInitializer {
  static init() {
    console.log('🚀 Initialisation de l\'application DCOP 413');

    // Initialiser les gestionnaires globaux
    window.notificationManager = new NotificationManager();
    window.navigationManager = new NavigationManager();
    window.formManager = new FormManager();
    window.photoManager = new PhotoManager();

    // Initialiser le gestionnaire de caméra si on est sur la page photo
    if (Utils.$('#video')) {
      window.cameraManager = new CameraManager();
    }

    // Exposer les classes pour utilisation externe
    window.AuthManager = AuthManager;
    window.Utils = Utils;

    // Initialisation spécifique par page
    AppInitializer.initPageSpecific();

    // Gestion des erreurs globales
    AppInitializer.setupErrorHandling();

    console.log('✅ Application initialisée avec succès');
  }

  static initPageSpecific() {
    const currentPage = window.navigationManager.currentPage;

    switch (currentPage) {
      case 'login':
        AppInitializer.initLoginPage();
        break;
      case 'accueil':
        AppInitializer.initAccueilPage();
        break;
      case 'formulaire':
        AppInitializer.initFormulairePage();
        break;
      case 'photo':
        AppInitializer.initPhotoPage();
        break;
    }
  }

  static initLoginPage() {
    console.log('📝 Initialisation page login');

    // Vérifier si déjà connecté
    if (AuthManager.isAuthenticated()) {
      window.location.href = 'accueil.html';
      return;
    }
  }

  static initAccueilPage() {
    console.log('🏠 Initialisation page accueil');

    // Vérifier l'authentification
    if (!AuthManager.requireAuth()) {
      return;
    }

    // Charger les données utilisateur
    AppInitializer.loadUserData();
    AppInitializer.loadStatistics();
  }

  static initFormulairePage() {
    console.log('📝 Initialisation page formulaire');

    // Vérifier l'authentification
    if (!AuthManager.requireAuth()) {
      return;
    }

    // Initialiser les champs de date/heure
    AppInitializer.initializeDateTimeFields();

    // Charger les données sauvegardées
    const form = Utils.$('#visitorForm');
    if (form && window.formManager) {
      window.formManager.loadFormDataFromSession(form);
    }

    // Charger la photo sauvegardée
    if (window.photoManager) {
      window.photoManager.loadSavedPhoto();
    }
  }

  static initPhotoPage() {
    console.log('📷 Initialisation page photo');

    // Vérifier l'authentification
    if (!AuthManager.requireAuth()) {
      return;
    }
  }

  static loadUserData() {
    try {
      const session = localStorage.getItem('user_session');
      if (session) {
        const userData = JSON.parse(session);
        console.log('👤 Utilisateur connecté:', userData.username);

        const userDisplay = Utils.$('#currentUser');
        if (userDisplay) {
          userDisplay.textContent = userData.username;
        }
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données utilisateur:', error);
    }
  }

  static loadStatistics() {
    setTimeout(() => {
      const todayVisitors = Utils.$('#todayVisitors');
      const monthlyTotal = Utils.$('#monthlyTotal');

      if (todayVisitors) {
        todayVisitors.textContent = Math.floor(Math.random() * 50) + 1;
      }

      if (monthlyTotal) {
        monthlyTotal.textContent = Math.floor(Math.random() * 500) + 100;
      }
    }, 500);
  }

  static initializeDateTimeFields() {
    const dateVisiteField = Utils.$('#date_visite');
    if (dateVisiteField) {
      const today = new Date().toISOString().split('T')[0];
      dateVisiteField.value = today;
      dateVisiteField.min = today;
    }

    const heureArriveeField = Utils.$('#heure_arrivee');
    if (heureArriveeField) {
      const now = new Date();
      const currentTime = now.getHours().toString().padStart(2, '0') + ':' +
                         now.getMinutes().toString().padStart(2, '0');
      heureArriveeField.value = currentTime;
    }
  }

  static setupErrorHandling() {
    window.addEventListener('error', (e) => {
      console.error('Erreur JavaScript:', e.error);

      if (window.notificationManager) {
        window.notificationManager.show('Une erreur inattendue s\'est produite', 'error');
      }
    });

    window.addEventListener('unhandledrejection', (e) => {
      console.error('Promise rejetée:', e.reason);

      if (window.notificationManager) {
        window.notificationManager.show('Erreur de connexion', 'error');
      }
    });
  }
}

// ===== DÉMARRAGE AUTOMATIQUE =====
document.addEventListener('DOMContentLoaded', () => {
  AppInitializer.init();
});

// Export pour utilisation dans d'autres modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    AuthManager,
    NavigationManager,
    FormManager,
    PhotoManager,
    CameraManager,
    NotificationManager,
    Utils,
    AppInitializer
  };
}
