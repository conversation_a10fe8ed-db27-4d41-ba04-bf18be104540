/**
 * Core Application - Configuration et fonctionnalités principales
 * DCOP 413 - Gestion des Visiteurs
 * Fusion optimisée de toutes les fonctionnalités JS
 */

'use strict';

// ===== CONFIGURATION GLOBALE =====
window.APP_CONFIG = {
  security: {
    MAX_FILE_SIZE: 5 * 1024 * 1024,
    ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
    MAX_IMAGE_DIMENSION: 2048,
    API_TIMEOUT: 30000,
    VALIDATION: {
      MIN_NAME_LENGTH: 2,
      MAX_NAME_LENGTH: 50,
      PHONE_PATTERN: /^(\+33|0)[1-9](\d{8})$/,
      EMAIL_PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    }
  },
  
  animations: {
    DURATION_FAST: 150,
    DURATION_NORMAL: 300,
    DURATION_SLOW: 500,
    EASING: 'cubic-bezier(0.4, 0, 0.2, 1)',
    SPRING: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)'
  },
  
  api: {
    LOGIN: '/login',
    LOGOUT: '/logout',
    VERIFY: '/auth/verify',
    SUBMIT_VISITOR: '/submit-visitor',
    PHOTO_UPLOAD: '/upload-photo'
  },
  
  messages: {
    errors: {
      REQUIRED_FIELD: 'Ce champ est obligatoire',
      INVALID_EMAIL: 'Adresse email invalide',
      INVALID_PHONE: 'Numéro de téléphone invalide',
      NETWORK_ERROR: 'Erreur de connexion. Veuillez réessayer.'
    },
    success: {
      LOGIN: 'Connexion réussie !',
      FORM_SUBMITTED: 'Formulaire soumis avec succès !',
      PHOTO_CAPTURED: 'Photo capturée avec succès !'
    }
  }
};

// ===== GESTIONNAIRE DE NOTIFICATIONS ÉLÉGANT =====
class NotificationManager {
  constructor() {
    this.container = null;
    this.init();
  }
  
  init() {
    this.container = document.createElement('div');
    this.container.className = 'notification-container fixed top-4 right-4 z-50 space-y-2';
    document.body.appendChild(this.container);
  }
  
  show(message, type = 'info', duration = 4000) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type} transform translate-x-full opacity-0 transition-all duration-300`;
    
    const icons = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️'
    };
    
    notification.innerHTML = `
      <div class="flex items-center gap-3 p-4 rounded-xl shadow-lg backdrop-blur-sm border">
        <span class="text-xl">${icons[type] || icons.info}</span>
        <span class="font-medium">${message}</span>
        <button class="ml-auto text-gray-400 hover:text-gray-600 transition-colors">
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
          </svg>
        </button>
      </div>
    `;
    
    this.container.appendChild(notification);
    
    // Animation d'entrée
    requestAnimationFrame(() => {
      notification.classList.remove('translate-x-full', 'opacity-0');
    });
    
    // Fermeture automatique
    setTimeout(() => this.remove(notification), duration);
    
    // Fermeture manuelle
    notification.querySelector('button').addEventListener('click', () => {
      this.remove(notification);
    });
    
    return notification;
  }
  
  remove(notification) {
    notification.classList.add('translate-x-full', 'opacity-0');
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }
  
  success(message) { return this.show(message, 'success'); }
  error(message) { return this.show(message, 'error'); }
  warning(message) { return this.show(message, 'warning'); }
  info(message) { return this.show(message, 'info'); }
}



// ===== GESTIONNAIRE DE FORMULAIRES AVANCÉ =====
class FormManager {
  constructor() {
    this.forms = new Map();
    this.validators = new Map();
    this.init();
  }
  
  init() {
    // Auto-détection des formulaires
    document.querySelectorAll('form').forEach(form => {
      this.registerForm(form);
    });
  }
  
  registerForm(form) {
    const formId = form.id || `form_${Date.now()}`;
    this.forms.set(formId, form);
    
    // Validation en temps réel
    form.querySelectorAll('input, textarea, select').forEach(field => {
      field.addEventListener('blur', () => this.validateField(field));
      field.addEventListener('input', this.debounce(() => this.validateField(field), 300));
    });
    
    // Soumission sécurisée
    form.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleSubmit(form);
    });
  }
  
  validateField(field) {
    const value = field.value.trim();
    const isRequired = field.hasAttribute('required');
    const fieldType = field.type || field.tagName.toLowerCase();
    
    // Supprimer les anciennes erreurs
    this.clearFieldError(field);
    
    // Validation obligatoire
    if (isRequired && !value) {
      this.showFieldError(field, 'Ce champ est obligatoire');
      return false;
    }
    
    // Validations spécifiques
    if (value) {
      switch (fieldType) {
        case 'email':
          if (!window.APP_CONFIG.security.VALIDATION.EMAIL_PATTERN.test(value)) {
            this.showFieldError(field, 'Adresse email invalide');
            return false;
          }
          break;
        case 'tel':
          if (!window.APP_CONFIG.security.VALIDATION.PHONE_PATTERN.test(value)) {
            this.showFieldError(field, 'Numéro de téléphone invalide');
            return false;
          }
          break;
      }
    }
    
    this.showFieldSuccess(field);
    return true;
  }
  
  showFieldError(field, message) {
    field.classList.add('border-red-500', 'bg-red-50');
    field.classList.remove('border-green-500', 'bg-green-50');
    
    let errorElement = field.parentNode.querySelector('.field-error');
    if (!errorElement) {
      errorElement = document.createElement('div');
      errorElement.className = 'field-error text-red-600 text-sm mt-1 font-medium';
      field.parentNode.appendChild(errorElement);
    }
    errorElement.textContent = message;
  }
  
  showFieldSuccess(field) {
    field.classList.add('border-green-500', 'bg-green-50');
    field.classList.remove('border-red-500', 'bg-red-50');
    this.clearFieldError(field);
  }
  
  clearFieldError(field) {
    field.classList.remove('border-red-500', 'bg-red-50', 'border-green-500', 'bg-green-50');
    const errorElement = field.parentNode.querySelector('.field-error');
    if (errorElement) {
      errorElement.remove();
    }
  }
  
  async handleSubmit(form) {
    // Validation complète
    const isValid = this.validateForm(form);
    if (!isValid) {
      window.notificationManager.error('Veuillez corriger les erreurs dans le formulaire');
      return;
    }
    
    // Soumission
    try {
      window.loadingManager.show('Envoi en cours...');
      
      const formData = new FormData(form);
      const response = await fetch(form.action || window.APP_CONFIG.api.SUBMIT_VISITOR, {
        method: 'POST',
        body: formData
      });
      
      if (response.ok) {
        window.notificationManager.success('Formulaire soumis avec succès !');
        form.reset();
      } else {
        throw new Error('Erreur lors de la soumission');
      }
    } catch (error) {
      window.notificationManager.error('Erreur lors de la soumission');
    } finally {
      window.loadingManager.hide();
    }
  }
  
  validateForm(form) {
    let isValid = true;
    form.querySelectorAll('input, textarea, select').forEach(field => {
      if (!this.validateField(field)) {
        isValid = false;
      }
    });
    return isValid;
  }
  
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
}

// ===== UTILITAIRES GLOBAUX =====
window.AppUtils = {
  formatDate(date) {
    return new Intl.DateTimeFormat('fr-FR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  },
  
  sanitizeString(str) {
    if (typeof str !== 'string') return '';
    return str.replace(/[<>'"&]/g, (char) => {
      const entities = {
        '<': '&lt;', '>': '&gt;', '"': '&quot;',
        "'": '&#x27;', '&': '&amp;'
      };
      return entities[char] || char;
    });
  },
  
  generateId() {
    return 'app_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
  }
};

// ===== GESTIONNAIRE DE PHOTO HEADER =====
class HeaderPhotoManager {
  constructor() {
    this.headerContainer = document.getElementById('headerPhotoContainer');
    this.headerPlaceholder = document.getElementById('headerPhotoPlaceholder');
    this.headerPreview = document.getElementById('headerPhotoPreview');
    this.formPhotoPreview = document.getElementById('visitorPhoto');
    this.formPlaceholder = document.getElementById('photoPlaceholder');
    this.init();
  }

  init() {
    // Écouter les changements de photo dans le formulaire
    if (this.formPhotoPreview) {
      const observer = new MutationObserver(() => {
        this.syncPhotoToHeader();
      });

      observer.observe(this.formPhotoPreview, {
        attributes: true,
        attributeFilter: ['src', 'style']
      });
    }

    // Écouter les événements de changement de photo
    document.addEventListener('photoChanged', (event) => {
      this.updateHeaderPhoto(event.detail.photoData);
    });
  }

  syncPhotoToHeader() {
    if (!this.headerContainer || !this.formPhotoPreview) return;

    const isVisible = this.formPhotoPreview.style.display !== 'none' && this.formPhotoPreview.src;

    if (isVisible) {
      this.updateHeaderPhoto(this.formPhotoPreview.src);
    } else {
      this.clearHeaderPhoto();
    }
  }

  updateHeaderPhoto(photoData) {
    if (!this.headerContainer) return;

    if (this.headerPreview && photoData) {
      this.headerPreview.src = photoData;
      this.headerPreview.style.display = 'block';
      if (this.headerPlaceholder) {
        this.headerPlaceholder.style.display = 'none';
      }
    }
  }

  clearHeaderPhoto() {
    if (!this.headerContainer) return;

    if (this.headerPreview) {
      this.headerPreview.style.display = 'none';
      this.headerPreview.src = '';
    }
    if (this.headerPlaceholder) {
      this.headerPlaceholder.style.display = 'block';
    }
  }
}

// ===== GESTIONNAIRE DE NAVIGATION FLUIDE =====
class NavigationManager {
  static navigateWithTransition(event, url) {
    if (event) {
      event.preventDefault();
    }

    // Animation de sortie
    document.body.style.transition = 'opacity 0.4s ease-out, transform 0.4s ease-out';
    document.body.style.opacity = '0.8';
    document.body.style.transform = 'scale(0.98)';

    // Redirection après animation
    setTimeout(() => {
      window.location.href = url;
    }, 400);
  }

  static initializePageTransitions() {
    // Animation d'entrée pour toutes les pages
    document.body.style.opacity = '0';
    document.body.style.transform = 'scale(1.02)';

    setTimeout(() => {
      document.body.style.transition = 'opacity 0.5s ease-out, transform 0.5s ease-out';
      document.body.style.opacity = '1';
      document.body.style.transform = 'scale(1)';
    }, 50);
  }
}

// Fonction globale pour la navigation
window.navigateWithTransition = NavigationManager.navigateWithTransition;

// ===== GESTIONNAIRE DES BOUTONS ET INTERACTIONS =====
class ButtonManager {
  static initializeButtons() {
    // Bouton d'impression
    const printBtn = document.getElementById('printBtn');
    if (printBtn) {
      printBtn.addEventListener('click', () => {
        window.print();
      });
    }

    // Bouton de soumission du formulaire
    const submitBtn = document.getElementById('submitBtn');
    const form = document.getElementById('fiche-reception');
    if (submitBtn && form) {
      submitBtn.addEventListener('click', (e) => {
        e.preventDefault();
        ButtonManager.handleFormSubmission(form);
      });
    }

    // Gestion des checkboxes de sécurité
    ButtonManager.initializeSecurityCheckboxes();

    // Gestion de la checkbox d'engagement
    ButtonManager.initializeEngagementCheckbox();

    // Initialiser la date d'impression
    ButtonManager.initializePrintDate();
  }

  static handleFormSubmission(form) {
    // Validation du formulaire
    if (!form.checkValidity()) {
      form.reportValidity();
      window.notificationManager?.show('Veuillez remplir tous les champs obligatoires', 'error');
      return;
    }

    // Vérification de l'engagement (maintenant une checkbox)
    const engagementAccepte = document.getElementById('engagement-accepte');
    if (!engagementAccepte?.checked) {
      window.notificationManager?.show('Vous devez accepter les conditions pour continuer', 'error');
      return;
    }

    // Simulation de l'envoi
    window.notificationManager?.show('Fiche de visiteur enregistrée avec succès !', 'success');

    // Optionnel : redirection après succès
    setTimeout(() => {
      if (confirm('Voulez-vous retourner à l\'accueil ?')) {
        window.navigateWithTransition(null, '/accueil');
      }
    }, 2000);
  }

  static initializePrintDate() {
    // Mettre à jour la date d'impression
    const printDateElement = document.getElementById('printDate');
    if (printDateElement) {
      const now = new Date();
      const dateString = now.toLocaleDateString('fr-FR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
      printDateElement.textContent = dateString;
    }
  }

  static initializeSecurityCheckboxes() {
    const securityCheckboxes = document.querySelectorAll('.ultra-checkbox-security');
    securityCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        const label = checkbox.nextElementSibling;
        if (checkbox.checked) {
          label.style.color = '#10b981';
          label.style.fontWeight = '600';
        } else {
          label.style.color = '#374151';
          label.style.fontWeight = '500';
        }
      });
    });
  }

  static initializeEngagementCheckbox() {
    const engagementCheckbox = document.getElementById('engagement-accepte');
    if (engagementCheckbox) {
      engagementCheckbox.addEventListener('change', () => {
        const container = engagementCheckbox.closest('.ultra-engagement-simple');

        if (engagementCheckbox.checked) {
          // Style quand coché
          container.style.borderColor = '#0284c7';
          container.style.background = 'linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%)';
          container.style.transform = 'translateY(-2px)';
          container.style.boxShadow = '0 8px 25px rgba(14, 165, 233, 0.3)';

          // Notification de succès
          window.notificationManager?.show('Merci d\'avoir accepté les conditions !', 'success');
        } else {
          // Style par défaut
          container.style.borderColor = '#0ea5e9';
          container.style.background = 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)';
          container.style.transform = 'translateY(0)';
          container.style.boxShadow = 'none';

          // Notification d'avertissement
          window.notificationManager?.show('Vous devez accepter les conditions pour continuer', 'warning');
        }
      });
    }
  }
}

// ===== GESTIONNAIRE DE PHOTO ET SELFIE =====
class PhotoManager {
  constructor() {
    this.photoFileInput = document.getElementById('photoFileInput');
    this.uploadPhotoBtn = document.getElementById('uploadPhotoBtn');
    this.capturePhotoBtn = document.getElementById('capturePhotoBtn');
    this.removePhotoBtn = document.getElementById('removePhotoBtn');
    this.takePictureBtn = document.getElementById('takePictureBtn');
    this.cancelCameraBtn = document.getElementById('cancelCameraBtn');
    this.cameraSection = document.getElementById('cameraSection');
    this.cameraVideo = document.getElementById('cameraVideo');
    this.cameraCanvas = document.getElementById('cameraCanvas');
    this.visitorPhoto = document.getElementById('visitorPhoto');
    this.photoPlaceholder = document.getElementById('photoPlaceholder');
    this.stream = null;

    this.initializePhotoHandlers();
  }

  initializePhotoHandlers() {
    // Bouton upload
    if (this.uploadPhotoBtn) {
      this.uploadPhotoBtn.addEventListener('click', () => {
        this.photoFileInput.click();
      });
    }

    // Input file
    if (this.photoFileInput) {
      this.photoFileInput.addEventListener('change', (e) => {
        this.handleFileUpload(e);
      });
    }

    // Bouton capture selfie
    if (this.capturePhotoBtn) {
      this.capturePhotoBtn.addEventListener('click', () => {
        this.startCamera();
      });
    }

    // Bouton supprimer
    if (this.removePhotoBtn) {
      this.removePhotoBtn.addEventListener('click', () => {
        this.removePhoto();
      });
    }

    // Bouton prendre photo
    if (this.takePictureBtn) {
      this.takePictureBtn.addEventListener('click', () => {
        this.takePicture();
      });
    }

    // Bouton annuler caméra
    if (this.cancelCameraBtn) {
      this.cancelCameraBtn.addEventListener('click', () => {
        this.stopCamera();
      });
    }
  }

  handleFileUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    // Vérifier le type de fichier
    if (!file.type.match(/^image\/(jpeg|png|webp)$/)) {
      window.notificationManager?.show('Format de fichier non supporté. Utilisez JPEG, PNG ou WebP.', 'error');
      return;
    }

    // Vérifier la taille (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      window.notificationManager?.show('La photo est trop volumineuse. Taille maximum : 5MB.', 'error');
      return;
    }

    // Lire le fichier
    const reader = new FileReader();
    reader.onload = (e) => {
      this.displayPhoto(e.target.result);
    };
    reader.readAsDataURL(file);
  }

  async startCamera() {
    try {
      this.stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'user',
          width: { ideal: 640 },
          height: { ideal: 480 }
        }
      });

      this.cameraVideo.srcObject = this.stream;
      this.cameraSection.style.display = 'block';

      window.notificationManager?.show('Caméra activée ! Positionnez-vous et cliquez sur Capturer.', 'success');
    } catch (error) {
      console.error('Erreur caméra:', error);
      window.notificationManager?.show('Impossible d\'accéder à la caméra. Vérifiez les permissions.', 'error');
    }
  }

  takePicture() {
    if (!this.stream) return;

    const context = this.cameraCanvas.getContext('2d');
    this.cameraCanvas.width = this.cameraVideo.videoWidth;
    this.cameraCanvas.height = this.cameraVideo.videoHeight;

    // Dessiner l'image de la vidéo sur le canvas
    context.drawImage(this.cameraVideo, 0, 0);

    // Convertir en data URL
    const photoData = this.cameraCanvas.toDataURL('image/jpeg', 0.8);

    // Afficher la photo
    this.displayPhoto(photoData);

    // Arrêter la caméra
    this.stopCamera();

    window.notificationManager?.show('Photo capturée avec succès !', 'success');
  }

  stopCamera() {
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }
    this.cameraSection.style.display = 'none';
  }

  displayPhoto(photoData) {
    this.visitorPhoto.src = photoData;
    this.visitorPhoto.style.display = 'block';
    this.photoPlaceholder.style.display = 'none';
    this.removePhotoBtn.style.display = 'inline-flex';

    // Synchroniser avec le header
    if (window.headerPhotoManager) {
      window.headerPhotoManager.updateHeaderPhoto(photoData);
    }

    // Déclencher l'événement pour la synchronisation
    document.dispatchEvent(new CustomEvent('photoChanged', {
      detail: { photoData }
    }));
  }

  removePhoto() {
    this.visitorPhoto.src = '';
    this.visitorPhoto.style.display = 'none';
    this.photoPlaceholder.style.display = 'flex';
    this.removePhotoBtn.style.display = 'none';
    this.photoFileInput.value = '';

    // Synchroniser avec le header
    if (window.headerPhotoManager) {
      window.headerPhotoManager.clearHeaderPhoto();
    }

    window.notificationManager?.show('Photo supprimée.', 'info');
  }
}

// ===== GESTIONNAIRE D'AUTHENTIFICATION =====
class AuthManager {
  static async login(username, password) {
    try {
      // Afficher une notification de connexion
      if (window.notificationManager) {
        window.notificationManager.show('🔄 Connexion en cours...', 'info');
      }

      // Simulation de connexion (remplacer par vraie API)
      if (username && password) {
        // Simuler un délai d'authentification
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Stocker les informations de session
        const sessionData = {
          token: "demo_token_" + Date.now(),
          username: username,
          loginTime: new Date().toISOString(),
          isAuthenticated: true
        };

        localStorage.setItem("auth_token", sessionData.token);
        localStorage.setItem("user_session", JSON.stringify(sessionData));

        // Notification de succès
        if (window.notificationManager) {
          window.notificationManager.show('✅ Connexion réussie !', 'success');
        }

        // Logique de redirection selon le contexte
        const redirectUrl = AuthManager.getRedirectUrl() || 'accueil.html';

        setTimeout(() => {
          if (window.navigationManager) {
            window.navigationManager.goToPage('accueil');
          } else {
            window.location.href = redirectUrl;
          }
        }, 800);

        return true;
      } else {
        throw new Error('Identifiants manquants');
      }

    } catch (error) {
      console.error("Erreur lors de la connexion:", error);

      if (window.notificationManager) {
        window.notificationManager.show('❌ Erreur de connexion', 'error');
      }

      return false;
    }
  }

  static getRedirectUrl() {
    // Récupérer l'URL de redirection stockée avant la connexion
    const redirectUrl = localStorage.getItem('redirect_after_login');
    localStorage.removeItem('redirect_after_login');
    return redirectUrl;
  }

  static setRedirectUrl(url) {
    // Stocker l'URL de redirection pour après la connexion
    localStorage.setItem('redirect_after_login', url);
  }

  static isAuthenticated() {
    const token = localStorage.getItem("auth_token");
    const session = localStorage.getItem("user_session");

    if (!token || !session) {
      return false;
    }

    try {
      const sessionData = JSON.parse(session);
      return sessionData.isAuthenticated && sessionData.token === token;
    } catch (error) {
      console.error("Erreur de validation de session:", error);
      return false;
    }
  }

  static requireAuth() {
    // Vérifier l'authentification et rediriger si nécessaire
    if (!AuthManager.isAuthenticated()) {
      // Stocker la page actuelle pour redirection après connexion
      AuthManager.setRedirectUrl(window.location.href);

      // Rediriger vers la page de connexion
      if (window.navigationManager) {
        window.navigationManager.goToPage('login');
      } else {
        window.location.href = 'login.html';
      }

      return false;
    }

    return true;
  }

  static async logout() {
    try {
      // Afficher une notification de déconnexion
      if (window.notificationManager) {
        window.notificationManager.show('🔄 Déconnexion en cours...', 'info');
      }

      // Animation de sortie
      document.body.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
      document.body.style.opacity = '0.7';
      document.body.style.transform = 'scale(0.98)';

      // Supprimer le token local
      localStorage.removeItem("auth_token");
      sessionStorage.clear();

      // Appeler l'endpoint de déconnexion
      const response = await fetch("/logout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: 'same-origin'
      });

      // Notification de succès
      if (window.notificationManager) {
        window.notificationManager.show('✅ Déconnexion réussie !', 'success');
      }

      // Redirection après un délai
      setTimeout(() => {
        window.location.href = "/login";
      }, 800);

    } catch (error) {
      console.error("Erreur lors de la déconnexion:", error);

      // Même en cas d'erreur, nettoyer et rediriger
      localStorage.removeItem("auth_token");
      sessionStorage.clear();

      if (window.notificationManager) {
        window.notificationManager.show('⚠️ Déconnexion forcée', 'warning');
      }

      setTimeout(() => {
        window.location.href = "/login";
      }, 1000);
    }
  }

  static async checkAuthStatus() {
    const token = localStorage.getItem("auth_token");
    if (!token) return false;

    try {
      const response = await fetch("/auth/verify", {
        headers: {
          "Authorization": `Bearer ${token}`
        }
      });

      if (response.ok) {
        return true;
      } else {
        // Token invalide, nettoyer
        localStorage.removeItem("auth_token");
        return false;
      }
    } catch (error) {
      console.error("Erreur lors de la vérification du token:", error);
      localStorage.removeItem("auth_token");
      return false;
    }
  }
}

// ===== INITIALISATION =====
document.addEventListener('DOMContentLoaded', () => {
  // Initialiser les gestionnaires
  window.notificationManager = new NotificationManager();
  window.formManager = new FormManager();
  window.headerPhotoManager = new HeaderPhotoManager();
  window.photoManager = new PhotoManager();
  window.authManager = AuthManager;

  // Initialiser les boutons et interactions
  ButtonManager.initializeButtons();

  // Ajouter les gestionnaires de déconnexion
  const logoutButtons = document.querySelectorAll('[href="/logout"], [data-action="logout"]');
  logoutButtons.forEach(button => {
    button.addEventListener('click', (e) => {
      e.preventDefault();
      AuthManager.logout();
    });
  });

  // Initialiser les transitions de page
  NavigationManager.initializePageTransitions();

  console.log('✅ Application Core initialisée');
});
