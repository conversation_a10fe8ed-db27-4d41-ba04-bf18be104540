/**
 * Navigation Manager pour DCOP 413
 * Gère la navigation fluide entre toutes les pages de l'application
 */

class NavigationManager {
  constructor() {
    this.currentPage = this.getCurrentPage();
    this.isTransitioning = false;
    this.transitionDuration = 300;
    
    this.initializeNavigation();
  }

  initializeNavigation() {
    this.setupNavigationEvents();
    this.updateActiveNavigation();
    this.setupPageTransitions();
    
    console.log('✅ NavigationManager initialisé');
  }

  getCurrentPage() {
    const path = window.location.pathname;
    const filename = path.split('/').pop() || 'index.html';
    
    // Mapping des pages
    const pageMap = {
      'index.html': 'accueil',
      'accueil.html': 'accueil',
      'login.html': 'login',
      'main.html': 'formulaire',
      'photo.html': 'photo',
      '': 'accueil'
    };
    
    return pageMap[filename] || 'accueil';
  }

  setupNavigationEvents() {
    // Gestion des liens de navigation
    document.addEventListener('click', (e) => {
      const link = e.target.closest('a[href]');
      if (link && this.isInternalLink(link)) {
        e.preventDefault();
        this.navigateToPage(link.href, link);
      }
    });

    // Gestion du bouton retour du navigateur
    window.addEventListener('popstate', (e) => {
      if (e.state && e.state.page) {
        this.handleBackNavigation(e.state.page);
      }
    });

    // Gestion des raccourcis clavier
    document.addEventListener('keydown', (e) => {
      this.handleKeyboardNavigation(e);
    });
  }

  isInternalLink(link) {
    const href = link.getAttribute('href');
    
    // Ignorer les liens externes, les ancres et les liens spéciaux
    if (!href || 
        href.startsWith('http') || 
        href.startsWith('mailto:') || 
        href.startsWith('tel:') || 
        href.startsWith('#') ||
        href === 'javascript:void(0)') {
      return false;
    }
    
    return true;
  }

  async navigateToPage(url, linkElement = null) {
    if (this.isTransitioning) return;
    
    this.isTransitioning = true;
    
    try {
      // Animation de sortie
      await this.animatePageOut();
      
      // Mise à jour de l'état de navigation
      if (linkElement) {
        this.updateNavigationState(linkElement);
      }
      
      // Navigation vers la nouvelle page
      window.location.href = url;
      
    } catch (error) {
      console.error('Erreur lors de la navigation:', error);
      this.isTransitioning = false;
    }
  }

  async animatePageOut() {
    const mainContent = document.querySelector('main, .modern-form-container-with-header');
    
    if (mainContent) {
      mainContent.style.transition = `all ${this.transitionDuration}ms ease-out`;
      mainContent.style.opacity = '0';
      mainContent.style.transform = 'translateY(-20px)';
      
      return new Promise(resolve => {
        setTimeout(resolve, this.transitionDuration);
      });
    }
  }

  setupPageTransitions() {
    // Animation d'entrée de page
    const mainContent = document.querySelector('main, .modern-form-container-with-header');
    
    if (mainContent) {
      // État initial
      mainContent.style.opacity = '0';
      mainContent.style.transform = 'translateY(20px)';
      
      // Animation d'entrée
      requestAnimationFrame(() => {
        mainContent.style.transition = `all ${this.transitionDuration}ms ease-out`;
        mainContent.style.opacity = '1';
        mainContent.style.transform = 'translateY(0)';
      });
    }
  }

  updateActiveNavigation() {
    // Mise à jour des liens actifs dans la navigation
    const navLinks = document.querySelectorAll('.header-nav-btn');
    
    navLinks.forEach(link => {
      link.classList.remove('active');
      
      const href = link.getAttribute('href');
      if (this.isCurrentPage(href)) {
        link.classList.add('active');
      }
    });
  }

  isCurrentPage(href) {
    if (!href) return false;
    
    const currentPath = window.location.pathname;
    const linkPath = href.includes('/') ? href : `/${href}`;
    
    return currentPath.endsWith(linkPath) || 
           (currentPath === '/' && href === 'accueil.html');
  }

  updateNavigationState(linkElement) {
    // Ajouter un état visuel au lien cliqué
    linkElement.style.transform = 'scale(0.95)';
    linkElement.style.opacity = '0.7';
    
    setTimeout(() => {
      linkElement.style.transform = '';
      linkElement.style.opacity = '';
    }, 150);
  }

  handleBackNavigation(page) {
    // Gestion de la navigation arrière
    this.currentPage = page;
    this.updateActiveNavigation();
  }

  handleKeyboardNavigation(e) {
    // Raccourcis clavier pour la navigation
    if (e.altKey) {
      switch (e.key) {
        case 'h':
        case 'Home':
          e.preventDefault();
          this.navigateToPage('accueil.html');
          break;
        case 'l':
          e.preventDefault();
          this.navigateToPage('login.html');
          break;
        case 'f':
          e.preventDefault();
          this.navigateToPage('main.html');
          break;
      }
    }
    
    // Navigation avec les flèches (si applicable)
    if (e.ctrlKey && e.key === 'ArrowLeft') {
      e.preventDefault();
      window.history.back();
    }
  }

  // Méthodes utilitaires pour les pages
  showLoadingState(element) {
    if (element) {
      element.style.opacity = '0.6';
      element.style.pointerEvents = 'none';
      
      // Ajouter un spinner si nécessaire
      const spinner = document.createElement('div');
      spinner.className = 'navigation-spinner';
      spinner.innerHTML = '⏳';
      spinner.style.cssText = `
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 1.5rem;
        animation: spin 1s linear infinite;
      `;
      
      element.style.position = 'relative';
      element.appendChild(spinner);
    }
  }

  hideLoadingState(element) {
    if (element) {
      element.style.opacity = '';
      element.style.pointerEvents = '';
      
      const spinner = element.querySelector('.navigation-spinner');
      if (spinner) {
        spinner.remove();
      }
    }
  }

  // Méthodes publiques pour utilisation externe
  goToPage(pageName) {
    const pageUrls = {
      'accueil': 'accueil.html',
      'login': 'login.html',
      'formulaire': 'main.html',
      'main': 'main.html',
      'photo': 'photo.html'
    };

    const url = pageUrls[pageName];
    if (url) {
      this.navigateToPage(url);
    } else {
      console.warn(`Page inconnue: ${pageName}`);
    }
  }

  // Fonction globale pour navigation simple
  static navigateTo(page) {
    if (window.navigationManager) {
      window.navigationManager.goToPage(page);
    } else {
      // Fallback si le manager n'est pas encore initialisé
      const pageUrls = {
        'accueil': 'accueil.html',
        'login': 'login.html',
        'formulaire': 'main.html',
        'main': 'main.html',
        'photo': 'photo.html'
      };

      const url = pageUrls[page];
      if (url) {
        window.location.href = url;
      }
    }
  }

  // Fonction pour valider les liens avant navigation
  validateAndNavigate(url) {
    // Vérifier si l'URL est valide
    if (!url || url === '#') {
      console.warn('URL invalide ou vide');
      return false;
    }

    // Vérifier si c'est un lien externe
    if (url.startsWith('http') || url.startsWith('mailto:') || url.startsWith('tel:')) {
      window.open(url, '_blank');
      return true;
    }

    // Navigation interne
    this.navigateToPage(url);
    return true;
  }

  getCurrentPageName() {
    return this.currentPage;
  }

  isOnPage(pageName) {
    return this.currentPage === pageName;
  }

  // Gestion des breadcrumbs
  updateBreadcrumbs() {
    const breadcrumbContainer = document.querySelector('.breadcrumbs');
    if (!breadcrumbContainer) return;
    
    const breadcrumbs = this.getBreadcrumbsForPage(this.currentPage);
    breadcrumbContainer.innerHTML = breadcrumbs.map(crumb => 
      `<span class="breadcrumb-item">${crumb}</span>`
    ).join(' > ');
  }

  getBreadcrumbsForPage(page) {
    const breadcrumbMap = {
      'accueil': ['Accueil'],
      'login': ['Connexion'],
      'formulaire': ['Accueil', 'Nouveau Visiteur'],
      'photo': ['Accueil', 'Nouveau Visiteur', 'Photo']
    };
    
    return breadcrumbMap[page] || ['Accueil'];
  }
}

// Styles CSS pour les animations
const navigationStyles = `
  @keyframes spin {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
  }
  
  .header-nav-btn.active {
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
  }
  
  .header-nav-btn:active {
    transform: scale(0.95);
  }
  
  .breadcrumbs {
    padding: 0.5rem 0;
    font-size: 0.875rem;
    color: #64748b;
  }
  
  .breadcrumb-item:not(:last-child)::after {
    content: ' › ';
    margin: 0 0.5rem;
    color: #94a3b8;
  }
`;

// Injection des styles
const styleSheet = document.createElement('style');
styleSheet.textContent = navigationStyles;
document.head.appendChild(styleSheet);

// Fonctions globales pour faciliter la navigation
window.navigateTo = function(page) {
  NavigationManager.navigateTo(page);
};

window.goToPage = function(page) {
  if (window.navigationManager) {
    window.navigationManager.goToPage(page);
  } else {
    NavigationManager.navigateTo(page);
  }
};

// Fonction pour gérer les liens avec confirmation
window.handleLinkClick = function(event, url, confirmMessage = null) {
  event.preventDefault();

  if (confirmMessage && !confirm(confirmMessage)) {
    return false;
  }

  if (window.navigationManager) {
    window.navigationManager.validateAndNavigate(url);
  } else {
    window.location.href = url;
  }

  return true;
};

// Fonction pour les fonctionnalités en développement
window.showDevelopmentAlert = function(featureName) {
  alert(`Fonctionnalité en développement : ${featureName}\n\nCette fonctionnalité sera disponible dans une prochaine version.`);
};

// Initialisation automatique
document.addEventListener('DOMContentLoaded', () => {
  window.navigationManager = new NavigationManager();

  // Ajouter des gestionnaires d'événements pour tous les liens
  document.addEventListener('click', (e) => {
    const link = e.target.closest('a[href]');
    if (link && link.getAttribute('href') === '#') {
      e.preventDefault();
      // Ne rien faire pour les liens vides
    }
  });
});

// Export pour utilisation dans d'autres modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = NavigationManager;
}
