<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Test des Styles - DCOP 413</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="css/professional-design.css" />
</head>
<body>

  <!-- Test Header -->
  <header class="ultra-modern-header">
    <div class="header-container">
      <div class="header-breadcrumb">
        <span class="breadcrumb-item">DCOP 413</span>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Test des Styles</span>
      </div>

      <div class="header-main-content">
        <div class="header-left-section">
          <div style="width: 80px; height: 80px; background: rgba(255,255,255,0.2); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">LOGO</div>
        </div>

        <div class="header-center-section">
          <h1 class="header-main-title">Test des Styles CSS</h1>
        </div>

        <div class="header-right-section">
          <div class="header-nav-buttons">
            <a href="#" class="header-nav-btn">🏠 Accueil</a>
            <a href="#" class="header-nav-btn">📋 Liste</a>
            <a href="#" class="header-nav-btn deconnexion">🚪 Déconnexion</a>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Test Formulaire -->
  <main class="modern-form-container-with-header">
    <div class="modern-form-wrapper">
      <form class="ultra-form-sections">
        
        <!-- Section Test -->
        <div class="ultra-form-section">
          <div class="ultra-section-header">
            <h2 class="ultra-section-title">🧪 Test des Composants</h2>
            <p class="ultra-section-description">Vérification de l'affichage des différents éléments CSS.</p>
          </div>

          <div class="ultra-form-grid">
            <!-- Test Champs -->
            <div class="ultra-field-half">
              <div class="ultra-field-group">
                <label class="ultra-label ultra-label-required">Champ Ultra</label>
                <input type="text" class="ultra-input" placeholder="Test ultra-input" />
              </div>
            </div>

            <div class="modern-field-half">
              <label class="modern-label">Champ Moderne</label>
              <div class="modern-input-wrapper">
                <input type="text" class="modern-input" placeholder="Test modern-input" />
              </div>
            </div>

            <div class="ultra-field-full">
              <div class="ultra-field-group">
                <label class="ultra-label">Zone de texte</label>
                <textarea class="ultra-input ultra-textarea" placeholder="Test textarea"></textarea>
              </div>
            </div>
          </div>
        </div>

        <!-- Test Boutons -->
        <div class="ultra-form-actions">
          <button type="button" class="ultra-btn ultra-btn-cancel">🏠 Annuler</button>
          <button type="button" class="ultra-btn ultra-btn-print">🖨️ Imprimer</button>
          <button type="button" class="ultra-btn ultra-btn-submit">✅ Valider</button>
        </div>

        <!-- Test Boutons Génériques -->
        <div style="display: flex; gap: 1rem; justify-content: center; margin-top: 2rem;">
          <button class="btn btn-secondary">Secondaire</button>
          <button class="btn btn-primary">Primaire</button>
          <button class="btn btn-success">Succès</button>
          <button class="btn btn-warning">Attention</button>
        </div>

      </form>
    </div>
  </main>

  <!-- Test Section Sécurité -->
  <div style="max-width: 1200px; margin: 2rem auto; padding: 0 2rem;">
    <div class="ultra-form-section">
      <div class="ultra-section-header">
        <h2 class="ultra-section-title">🔒 Test Sécurité</h2>
        <p class="ultra-section-description">Test des composants de sécurité.</p>
      </div>

      <div class="ultra-security-rules">
        <div class="ultra-security-rule">
          <div class="ultra-security-icon">🏷️</div>
          <div class="ultra-security-content">
            <h4 class="ultra-security-title">Test Règle</h4>
            <p class="ultra-security-description">Description de test pour vérifier l'affichage.</p>
            <div class="ultra-security-checkbox">
              <input type="checkbox" checked disabled class="ultra-checkbox-security" />
              <label class="ultra-checkbox-label-security">J'ai pris connaissance</label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Test Page Photo -->
  <div style="background: linear-gradient(135deg, #2563eb 0%, #1e3a8a 100%); padding: 2rem; margin-top: 2rem;">
    <div class="photo-container">
      <div class="photo-header">
        <h1 class="photo-title">Test Photo</h1>
        <p class="photo-subtitle">Test des styles de la page photo</p>
      </div>
      
      <div style="display: flex; gap: 1rem; justify-content: center;">
        <button class="btn btn-primary">📷 Prendre</button>
        <button class="btn btn-secondary">❌ Annuler</button>
      </div>
    </div>
  </div>

  <script>
    // Test JavaScript
    document.addEventListener('DOMContentLoaded', () => {
      console.log('✅ Test des styles chargé');
      console.log('📊 Styles CSS:', {
        professionalDesign: !!document.querySelector('link[href*="professional-design.css"]'),
        tailwind: !!document.querySelector('script[src*="tailwindcss.com"]')
      });
      
      // Test des classes CSS
      const testElements = [
        '.ultra-modern-header',
        '.ultra-form-section',
        '.ultra-btn',
        '.btn',
        '.photo-container'
      ];
      
      testElements.forEach(selector => {
        const element = document.querySelector(selector);
        console.log(`${element ? '✅' : '❌'} ${selector}:`, element ? 'Trouvé' : 'Manquant');
      });
    });
  </script>

</body>
</html>
