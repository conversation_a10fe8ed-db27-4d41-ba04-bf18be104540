<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Accueil Élégant - DCOP 413</title>
  <meta name="description" content="Interface d'accueil professionnelle pour la gestion des visiteurs">

  <!-- <PERSON>onts Google -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

  <!-- Styles -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="../css/professional-design.css" />

  <!-- Scripts -->
  <script src="../js_ts/navigation-manager.js"></script>
  <script src="../js_ts/app-core.js"></script>
</head>
<body>

  <!-- Header Ultra-Moderne pour Accueil -->
  <header class="ultra-modern-header">
    <div class="header-container">
      <!-- Ligne du haut : Logo à gauche -->
      <div class="header-top-row">
        <!-- Logo en haut à gauche -->
        <div class="header-logo-section">
          <img src="/imgs/logo_cnc.png" alt="Logo DCOP 413" class="header-logo" />
        </div>
      </div>

      <!-- Ligne principale : Titre centré, navigation et photo -->
      <div class="header-main-content">
        <!-- Titre centré -->
        <div class="header-center-section">
          <h1 class="header-main-title">DCOP 413</h1>
          <p class="header-subtitle">Système de Gestion des Visiteurs</p>
        </div>

        <!-- Section photo à droite -->
        <div class="header-photo-section">
          <div class="visitor-photo-container" id="headerPhotoContainer">
            <div class="photo-placeholder">
              <span class="photo-icon">🏠</span>
              <span class="photo-text">Accueil</span>
            </div>
          </div>
        </div>

        <!-- Navigation à droite -->
        <div class="header-right-section">
          <div class="header-nav-buttons">
            <a href="main.html" class="header-nav-btn">📝 Nouveau Visiteur</a>
            <a href="liste-visiteurs.html" class="header-nav-btn">👥 Visiteurs</a>
            <a href="recherche.html" class="header-nav-btn">🔍 Recherche</a>
            <a href="rapports.html" class="header-nav-btn">📊 Rapports</a>
            <a href="login.html" class="header-nav-btn deconnexion">🚪 Déconnexion</a>
          </div>
        </div>
      </div>
    </div>

  </header>

  <!-- Conteneur principal avec header -->
  <main role="main" class="modern-form-container-with-header bg-gray-50 min-h-screen">
    <div class="modern-form-wrapper max-w-6xl mx-auto px-4 py-8">

      <!-- Section Bienvenue -->
      <div class="ultra-form-section bg-white rounded-xl shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300">
        <div class="ultra-section-header bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-xl p-6 border-b border-gray-200">
          <h2 class="ultra-section-title text-2xl font-bold text-gray-800 flex items-center gap-3">
            <span class="text-3xl">🎉</span>
            Bienvenue dans le Système DCOP 413
          </h2>
          <p class="ultra-section-description text-gray-600 mt-2 leading-relaxed">
            Interface moderne et sécurisée pour la gestion des visiteurs de la Direction de la Coopération Opérationnelle
          </p>
        </div>

        <div class="p-6">
          <!-- Statistiques en temps réel -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="stat-card">
              <div class="stat-card-content bg-gradient-to-br from-blue-500 to-purple-600 text-white p-6 rounded-xl text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                <div class="text-4xl mb-3">👥</div>
                <div class="text-3xl font-bold mb-2" id="todayVisitors">0</div>
                <div class="text-sm opacity-90">Visiteurs aujourd'hui</div>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-card-content bg-gradient-to-br from-pink-500 to-red-500 text-white p-6 rounded-xl text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                <div class="text-4xl mb-3">📊</div>
                <div class="text-3xl font-bold mb-2" id="monthlyTotal">0</div>
                <div class="text-sm opacity-90">Total ce mois</div>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-card-content bg-gradient-to-br from-cyan-500 to-blue-500 text-white p-6 rounded-xl text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                <div class="text-4xl mb-3">🔒</div>
                <div class="text-3xl font-bold mb-2">100%</div>
                <div class="text-sm opacity-90">Sécurité active</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions Principales -->
      <div class="ultra-form-section bg-white rounded-xl shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300">
        <div class="ultra-section-header bg-gradient-to-r from-green-50 to-emerald-50 rounded-t-xl p-6 border-b border-gray-200">
          <h2 class="ultra-section-title text-2xl font-bold text-gray-800 flex items-center gap-3">
            <span class="text-3xl">🚀</span>
            Actions Rapides
          </h2>
          <p class="ultra-section-description text-gray-600 mt-2 leading-relaxed">
            Accédez rapidement aux fonctionnalités principales du système de gestion des visiteurs
          </p>
        </div>

        <div class="p-6">
          <!-- Bouton principal -->
          <div class="text-center mb-8">
            <a href="main.html" id="newVisitorBtn" class="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-1">
              <span class="text-xl">➕</span>
              Enregistrer un Nouveau Visiteur
            </a>
          </div>

          <!-- Actions secondaires -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <a href="liste-visiteurs.html" class="action-card group">
              <div class="bg-white border-2 border-gray-200 rounded-xl p-6 transition-all duration-300 group-hover:border-blue-400 group-hover:shadow-lg group-hover:-translate-y-1">
                <div class="flex items-center gap-4">
                  <div class="text-3xl">📋</div>
                  <div>
                    <div class="font-semibold text-gray-800 mb-1">Consulter les Fiches</div>
                    <div class="text-sm text-gray-600">Voir tous les visiteurs enregistrés</div>
                  </div>
                </div>
              </div>
            </a>

            <a href="recherche.html" class="action-card group">
              <div class="bg-white border-2 border-gray-200 rounded-xl p-6 transition-all duration-300 group-hover:border-green-400 group-hover:shadow-lg group-hover:-translate-y-1">
                <div class="flex items-center gap-4">
                  <div class="text-3xl">🔍</div>
                  <div>
                    <div class="font-semibold text-gray-800 mb-1">Recherche Avancée</div>
                    <div class="text-sm text-gray-600">Rechercher des visiteurs spécifiques</div>
                  </div>
                </div>
              </div>
            </a>

            <a href="rapports.html" class="action-card group">
              <div class="bg-white border-2 border-gray-200 rounded-xl p-6 transition-all duration-300 group-hover:border-purple-400 group-hover:shadow-lg group-hover:-translate-y-1">
                <div class="flex items-center gap-4">
                  <div class="text-3xl">📈</div>
                  <div>
                    <div class="font-semibold text-gray-800 mb-1">Rapports</div>
                    <div class="text-sm text-gray-600">Statistiques et analyses détaillées</div>
                  </div>
                </div>
              </div>
            </a>
          </div>
        </div>
      </div>

    </div>
  </main>



  <!-- Script pour vérifier l'authentification -->
  <script>
    document.addEventListener("DOMContentLoaded", async () => {
      try {
        const response = await fetch("/auth/verify");
        if (!response.ok) {
          // Non authentifié, rediriger vers la page de connexion
          window.location.href = "/login";
        }
      } catch (error) {
        console.error("Erreur lors de la vérification d'authentification:", error);
        // En cas d'erreur, rediriger vers la page de connexion
        window.location.href = "/login";
      }
    });

    // Fonction de déconnexion
    async function logout() {
      try {
        // Supprimer le token local
        localStorage.removeItem("auth_token");

        // Appeler l'endpoint de déconnexion
        await fetch("/logout");

        // Rediriger vers la page de connexion
        window.location.href = "/login";
      } catch (error) {
        console.error("Erreur lors de la déconnexion:", error);
        // Même en cas d'erreur, rediriger vers la page de connexion
        window.location.href = "/login";
      }
    }

    // Navigation avec transition fluide
    function navigateWithTransition(event, url) {
      event.preventDefault();

      // Animation de sortie
      document.body.style.transition = 'opacity 0.4s ease-out, transform 0.4s ease-out';
      document.body.style.opacity = '0.8';
      document.body.style.transform = 'scale(0.98)';

      // Redirection après animation
      setTimeout(() => {
        window.location.href = url;
      }, 400);
    }

    // Animation d'entrée de la page
    document.addEventListener('DOMContentLoaded', () => {
      document.body.style.opacity = '0';
      document.body.style.transform = 'scale(1.02)';
      document.body.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';

      setTimeout(() => {
        document.body.style.opacity = '1';
        document.body.style.transform = 'scale(1)';
      }, 100);
    });
  </script>

</body>
</html>

