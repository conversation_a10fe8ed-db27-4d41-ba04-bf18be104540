<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Accueil Élégant - DCOP 413</title>
  <meta name="description" content="Interface d'accueil professionnelle pour la gestion des visiteurs">

  <!-- <PERSON>onts Google -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

  <!-- Styles -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="../css/professional-design.css" />

  <!-- Scripts -->
  <script src="../js_ts/navigation-manager.js"></script>
  <script src="../js_ts/app-core.js"></script>

  <!-- Script de protection d'authentification -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Vérifier l'authentification avant d'afficher la page
      if (window.AuthManager && !window.AuthManager.isAuthenticated()) {
        console.log('🔒 Accès non autorisé - Redirection vers login');
        window.location.href = 'login.html';
        return;
      }

      // Charger les données utilisateur si authentifié
      loadUserData();

      // Initialiser les statistiques
      loadStatistics();
    });

    function loadUserData() {
      try {
        const session = localStorage.getItem('user_session');
        if (session) {
          const userData = JSON.parse(session);
          console.log('👤 Utilisateur connecté:', userData.username);

          // Afficher le nom d'utilisateur si élément disponible
          const userDisplay = document.getElementById('currentUser');
          if (userDisplay) {
            userDisplay.textContent = userData.username;
          }
        }
      } catch (error) {
        console.error('Erreur lors du chargement des données utilisateur:', error);
      }
    }

    function loadStatistics() {
      // Simuler le chargement des statistiques
      setTimeout(() => {
        const todayVisitors = document.getElementById('todayVisitors');
        const monthlyTotal = document.getElementById('monthlyTotal');

        if (todayVisitors) {
          todayVisitors.textContent = Math.floor(Math.random() * 50) + 1;
        }

        if (monthlyTotal) {
          monthlyTotal.textContent = Math.floor(Math.random() * 500) + 100;
        }
      }, 500);
    }

    // Fonction pour déconnexion avec confirmation
    function handleLogout(event) {
      event.preventDefault();

      if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
        if (window.AuthManager) {
          window.AuthManager.logout();
        } else {
          localStorage.clear();
          window.location.href = 'login.html';
        }
      }
    }

    // Fonction pour navigation vers nouveau visiteur avec vérification
    function goToNewVisitor() {
      // Vérifier l'authentification avant de naviguer
      if (window.AuthManager && !window.AuthManager.isAuthenticated()) {
        alert('Session expirée. Veuillez vous reconnecter.');
        window.location.href = 'login.html';
        return;
      }

      // Naviguer vers le formulaire
      if (window.navigationManager) {
        window.navigationManager.goToPage('formulaire');
      } else {
        window.location.href = 'main.html';
      }
    }
  </script>
</head>
<body>

  <!-- Header Ultra-Moderne pour Accueil -->
  <header class="ultra-modern-header">
    <div class="header-container">
      <!-- Ligne du haut : Logo à gauche -->
      <div class="header-top-row">
        <!-- Logo en haut à gauche -->
        <div class="header-logo-section">
          <img src="/imgs/logo_cnc.png" alt="Logo DCOP 413" class="header-logo" />
        </div>
      </div>

      <!-- Ligne principale : Titre centré et navigation à droite -->
      <div class="header-main-content">
        <!-- Titre centré -->
        <div class="header-center-section">
          <h1 class="header-main-title">DCOP 413</h1>
        </div>

        <!-- Navigation à droite -->
        <div class="header-right-section">
          <div class="header-nav-buttons">
            <a href="main.html" class="header-nav-btn">Nouveau Visiteur</a>
            <a href="photo.html" class="header-nav-btn">Photo</a>
            <a href="#recherche" class="header-nav-btn" onclick="alert('Fonctionnalité en développement')">Recherche</a>
            <a href="#rapports" class="header-nav-btn" onclick="alert('Fonctionnalité en développement')">Rapports</a>
            <a href="#" onclick="handleLogout(event)" class="header-nav-btn deconnexion">Déconnexion</a>
          </div>
        </div>
      </div>
    </div>

  </header>

  <!-- Conteneur principal avec header -->
  <main class="modern-form-container-with-header">
    <div class="modern-form-wrapper">

      <!-- Section Bienvenue -->
      <div class="ultra-form-section">
        <div class="ultra-section-header">
          <h2 class="ultra-section-title">🎉 Bienvenue dans le Système</h2>
          <p class="ultra-section-description">Interface moderne et sécurisée pour la gestion des visiteurs de la DCOP 413</p>
        </div>

        <!-- Statistiques en temps réel -->
        <div class="ultra-form-grid">
          <div class="ultra-field-third">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 2rem; border-radius: 16px; text-align: center;">
              <div style="font-size: 2.5rem; margin-bottom: 0.5rem;">👥</div>
              <div style="font-size: 2rem; font-weight: 700; margin-bottom: 0.5rem;">0</div>
              <div style="font-size: 0.875rem; opacity: 0.9;">Visiteurs aujourd'hui</div>
            </div>
          </div>
          <div class="ultra-field-third">
            <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 2rem; border-radius: 16px; text-align: center;">
              <div style="font-size: 2.5rem; margin-bottom: 0.5rem;">📊</div>
              <div style="font-size: 2rem; font-weight: 700; margin-bottom: 0.5rem;">0</div>
              <div style="font-size: 0.875rem; opacity: 0.9;">Total ce mois</div>
            </div>
          </div>
          <div class="ultra-field-third">
            <div style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 2rem; border-radius: 16px; text-align: center;">
              <div style="font-size: 2.5rem; margin-bottom: 0.5rem;">🔒</div>
              <div style="font-size: 2rem; font-weight: 700; margin-bottom: 0.5rem;">100%</div>
              <div style="font-size: 0.875rem; opacity: 0.9;">Sécurité active</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions Principales -->
      <div class="ultra-form-section">
        <div class="ultra-section-header">
          <h2 class="ultra-section-title">🚀 Actions Rapides</h2>
          <p class="ultra-section-description">Accédez rapidement aux fonctionnalités principales du système</p>
        </div>

        <div class="ultra-form-grid">
          <div class="ultra-field-full" style="text-align: center; margin-bottom: 2rem;">
            <button onclick="goToNewVisitor()" id="newVisitorBtn" style="display: inline-flex; align-items: center; gap: 0.75rem; padding: 1rem 2rem; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 12px; font-weight: 600; font-size: 1.125rem; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4); cursor: pointer;" onmouseover="this.style.transform='translateY(-2px) scale(1.02)'; this.style.boxShadow='0 8px 25px rgba(102, 126, 234, 0.5)'" onmouseout="this.style.transform='translateY(0) scale(1)'; this.style.boxShadow='0 4px 15px rgba(102, 126, 234, 0.4)'">
              ➕ Enregistrer un Nouveau Visiteur
            </button>
          </div>

          <div class="ultra-field-half">
            <a href="#" onclick="alert('Fonctionnalité en développement - Liste des visiteurs')" style="display: flex; align-items: center; gap: 1rem; padding: 1.5rem; background: white; border: 2px solid #e2e8f0; border-radius: 12px; text-decoration: none; color: #374151; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#667eea'; this.style.transform='translateY(-2px)'" onmouseout="this.style.borderColor='#e2e8f0'; this.style.transform='translateY(0)'">
              <div style="font-size: 2rem;">📋</div>
              <div>
                <div style="font-weight: 600; margin-bottom: 0.25rem;">Consulter les Fiches</div>
                <div style="font-size: 0.875rem; color: #64748b;">Voir tous les visiteurs enregistrés</div>
              </div>
            </a>
          </div>

          <div class="ultra-field-half">
            <a href="#" onclick="alert('Fonctionnalité en développement - Statistiques et rapports')" style="display: flex; align-items: center; gap: 1rem; padding: 1.5rem; background: white; border: 2px solid #e2e8f0; border-radius: 12px; text-decoration: none; color: #374151; transition: all 0.3s ease; cursor: pointer;" onmouseover="this.style.borderColor='#667eea'; this.style.transform='translateY(-2px)'" onmouseout="this.style.borderColor='#e2e8f0'; this.style.transform='translateY(0)'">
              <div style="font-size: 2rem;">📈</div>
              <div>
                <div style="font-weight: 600; margin-bottom: 0.25rem;">Statistiques</div>
                <div style="font-size: 0.875rem; color: #64748b;">Rapports et analyses détaillées</div>
              </div>
            </a>
          </div>
        </div>
      </div>

    </div>
  </main>



  <!-- Script pour vérifier l'authentification -->
  <script>
    document.addEventListener("DOMContentLoaded", async () => {
      try {
        const response = await fetch("/auth/verify");
        if (!response.ok) {
          // Non authentifié, rediriger vers la page de connexion
          window.location.href = "/login";
        }
      } catch (error) {
        console.error("Erreur lors de la vérification d'authentification:", error);
        // En cas d'erreur, rediriger vers la page de connexion
        window.location.href = "/login";
      }
    });

    // Fonction de déconnexion
    async function logout() {
      try {
        // Supprimer le token local
        localStorage.removeItem("auth_token");

        // Appeler l'endpoint de déconnexion
        await fetch("/logout");

        // Rediriger vers la page de connexion
        window.location.href = "/login";
      } catch (error) {
        console.error("Erreur lors de la déconnexion:", error);
        // Même en cas d'erreur, rediriger vers la page de connexion
        window.location.href = "/login";
      }
    }

    // Navigation avec transition fluide
    function navigateWithTransition(event, url) {
      event.preventDefault();

      // Animation de sortie
      document.body.style.transition = 'opacity 0.4s ease-out, transform 0.4s ease-out';
      document.body.style.opacity = '0.8';
      document.body.style.transform = 'scale(0.98)';

      // Redirection après animation
      setTimeout(() => {
        window.location.href = url;
      }, 400);
    }

    // Animation d'entrée de la page
    document.addEventListener('DOMContentLoaded', () => {
      document.body.style.opacity = '0';
      document.body.style.transform = 'scale(1.02)';
      document.body.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';

      setTimeout(() => {
        document.body.style.opacity = '1';
        document.body.style.transform = 'scale(1)';
      }, 100);
    });
  </script>

</body>
</html>

