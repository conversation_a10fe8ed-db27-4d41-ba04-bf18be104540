<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Accueil Élégant - DCOP 413</title>
  <meta name="description" content="Interface d'accueil professionnelle pour la gestion des visiteurs">

  <!-- <PERSON>onts Google -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

  <!-- Styles -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="/css/unified-styles.css" />
  <link rel="stylesheet" href="/css/dynamic-styles.css" />
  <link rel="stylesheet" href="/css/accueil-specific.css" />
  <link rel="stylesheet" href="/css/dashboard-styles.css" />

  <!-- Scripts -->
  <script src="/js/navigation-config.js"></script>
  <script src="/js/auth-guard.js"></script>
  <script src="/js/app-core.js"></script>
  <script src="/js/unified-app.js"></script>
  <script src="/js/dashboard.js"></script>
  <script src="/js/accueil-events.js"></script>


</head>
<body>

  <!-- Header Ultra-Moderne pour Accueil -->
  <header class="ultra-modern-header">
    <div class="header-container">
      <!-- Ligne du haut : Logo à gauche -->
      <div class="header-top-row">
        <!-- Logo en haut à gauche -->
        <div class="header-logo-section">
          <img src="/imgs/logo_cnc.png" alt="Logo DCOP 413" class="header-logo" />
        </div>
      </div>

      <!-- Ligne principale : Titre centré et navigation à droite -->
      <div class="header-main-content">
        <!-- Titre centré -->
        <div class="header-center-section">
          <h1 class="header-main-title">DCOP 413</h1>
        </div>

        <!-- Navigation à droite -->
        <div class="header-right-section">
          <div class="header-nav-buttons">
            <a href="main.html" class="header-nav-btn">Nouveau Visiteur</a>
            <a href="photo.html" class="header-nav-btn">Photo</a>
            <a href="#recherche" class="header-nav-btn" data-dev-feature="Recherche">Recherche</a>
            <a href="#rapports" class="header-nav-btn" data-dev-feature="Rapports">Rapports</a>
            <a href="#" data-action="logout" class="header-nav-btn deconnexion">Déconnexion</a>
          </div>
        </div>
      </div>
    </div>

  </header>

  <!-- Conteneur principal avec header -->
  <main class="modern-form-container-with-header">
    <div class="modern-form-wrapper">

      <!-- Section Bienvenue -->
      <div class="ultra-form-section">
        <div class="ultra-section-header">
          <h2 class="ultra-section-title">🎉 Bienvenue dans le Système</h2>
          <p class="ultra-section-description">Interface moderne et sécurisée pour la gestion des visiteurs de la DCOP 413</p>
        </div>

        <!-- Tableau de bord temps réel -->
        <div class="dashboard-controls mb-6">
          <div class="flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-800">📊 Métriques Temps Réel</h3>
            <div class="flex gap-3">
              <button id="refreshDashboard" class="refresh-btn text-sm">
                🔄 Actualiser
              </button>
              <label class="auto-update-toggle text-sm">
                <input type="checkbox" id="autoUpdateToggle" checked class="mr-2">
                Auto-update
              </label>
            </div>
          </div>
        </div>

        <!-- Grille de métriques avancées -->
        <div class="metrics-grid mb-8">
          <div id="totalRequests" class="metric-card">
            <div class="metric-icon">📊</div>
            <span class="metric-value">0</span>
            <span class="metric-label">Requêtes Totales</span>
          </div>
          <div id="activeSessions" class="metric-card">
            <div class="metric-icon">👥</div>
            <span class="metric-value">0</span>
            <span class="metric-label">Sessions Actives</span>
          </div>
          <div id="avgResponseTime" class="metric-card">
            <div class="metric-icon">⚡</div>
            <span class="metric-value">0ms</span>
            <span class="metric-label">Temps de Réponse</span>
          </div>
          <div id="errorRate" class="metric-card">
            <div class="metric-icon">⚠️</div>
            <span class="metric-value">0%</span>
            <span class="metric-label">Taux d'Erreur</span>
          </div>
          <div id="securityEvents" class="metric-card">
            <div class="metric-icon">🔒</div>
            <span class="metric-value">0</span>
            <span class="metric-label">Événements Sécurité</span>
          </div>
          <div id="uptime" class="metric-card">
            <div class="metric-icon">⏱️</div>
            <span class="metric-value">0h</span>
            <span class="metric-label">Temps de Fonctionnement</span>
          </div>
        </div>

        <!-- Statut de sécurité -->
        <div id="securityStatus" class="security-status secure mb-6">
          <div class="security-icon">🛡️</div>
          <div class="security-text">
            <div class="security-level">SÉCURISÉ</div>
            <div class="security-details">Tous les systèmes fonctionnent normalement</div>
          </div>
        </div>

        <!-- Statistiques existantes -->
        <div class="ultra-form-grid">
          <div class="ultra-field-third">
            <div class="stats-card-visitors">
              <div class="stats-card-icon">👥</div>
              <div class="stats-card-number" id="todayVisitors">0</div>
              <div class="stats-card-label">Visiteurs aujourd'hui</div>
            </div>
          </div>
          <div class="ultra-field-third">
            <div class="stats-card-monthly">
              <div class="stats-card-icon">📊</div>
              <div class="stats-card-number" id="monthlyTotal">0</div>
              <div class="stats-card-label">Total ce mois</div>
            </div>
          </div>
          <div class="ultra-field-third">
            <div class="stats-card-security">
              <div class="stats-card-icon">🔒</div>
              <div class="stats-card-number">100%</div>
              <div class="stats-card-label">Sécurité active</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions Principales -->
      <div class="ultra-form-section">
        <div class="ultra-section-header">
          <h2 class="ultra-section-title">🚀 Actions Rapides</h2>
          <p class="ultra-section-description">Accédez rapidement aux fonctionnalités principales du système</p>
        </div>

        <div class="ultra-form-grid">
          <div class="ultra-field-full actions-section">
            <button id="newVisitorBtn" class="new-visitor-btn">
              ➕ Enregistrer un Nouveau Visiteur
            </button>
          </div>

          <div class="ultra-field-half">
            <a href="#" class="action-card" data-action="view-visitors">
              <div class="action-card-icon">📋</div>
              <div class="action-card-content">
                <div class="action-card-title">Consulter les Fiches</div>
                <div class="action-card-description">Voir tous les visiteurs enregistrés</div>
              </div>
            </a>
          </div>

          <div class="ultra-field-half">
            <a href="#" class="action-card" data-action="statistics">
              <div class="action-card-icon">📈</div>
              <div class="action-card-content">
                <div class="action-card-title">Statistiques</div>
                <div class="action-card-description">Rapports et analyses détaillées</div>
              </div>
            </a>
          </div>
        </div>
      </div>

    </div>
  </main>



  <!-- Script pour vérifier l'authentification -->
  <script>
    document.addEventListener("DOMContentLoaded", async () => {
      try {
        const response = await fetch("/auth/verify");
        if (!response.ok) {
          // Non authentifié, rediriger vers la page de connexion
          window.location.href = "/login";
        }
      } catch (error) {
        console.error("Erreur lors de la vérification d'authentification:", error);
        // En cas d'erreur, rediriger vers la page de connexion
        window.location.href = "/login";
      }
    });

    // Fonction de déconnexion
    async function logout() {
      try {
        // Supprimer le token local
        localStorage.removeItem("auth_token");

        // Appeler l'endpoint de déconnexion
        await fetch("/logout");

        // Rediriger vers la page de connexion
        window.location.href = "/login";
      } catch (error) {
        console.error("Erreur lors de la déconnexion:", error);
        // Même en cas d'erreur, rediriger vers la page de connexion
        window.location.href = "/login";
      }
    }

    // Navigation avec transition fluide
    function navigateWithTransition(event, url) {
      event.preventDefault();

      // Animation de sortie
      document.body.style.transition = 'opacity 0.4s ease-out, transform 0.4s ease-out';
      document.body.style.opacity = '0.8';
      document.body.style.transform = 'scale(0.98)';

      // Redirection après animation
      setTimeout(() => {
        window.location.href = url;
      }, 400);
    }

    // Animation d'entrée de la page
    document.addEventListener('DOMContentLoaded', () => {
      document.body.style.opacity = '0';
      document.body.style.transform = 'scale(1.02)';
      document.body.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';

      setTimeout(() => {
        document.body.style.opacity = '1';
        document.body.style.transform = 'scale(1)';
      }, 100);
    });
  </script>

</body>
</html>

