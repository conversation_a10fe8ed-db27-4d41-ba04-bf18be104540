<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Formulaire de réception des visiteurs - DCOP 413 - Gestion sécurisée des accès" />
    <meta name="keywords" content="visiteur, réception, DCOP, sécurité, accès" />
    <meta name="author" content="DCOP 413" />
    <meta name="robots" content="noindex, nofollow" />

    <!-- Sécurité CSP -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com; style-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com; img-src 'self' data: blob:; font-src 'self' https://fonts.gstatic.com;" />

    <title>Fiche de Réception - DCOP 413</title>
    <link rel="icon" href="/favicon.ico" type="image/x-icon" />

    <!-- Préchargement des ressources critiques -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" as="style" />
    <link rel="preload" href="../css/professional-design.css" as="style" />
    <link rel="preload" href="../js_ts/app-core.js" as="script" />

    <!-- Fonts Google -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Styles -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="../css/professional-design.css" />

    <!-- Scripts unifiés -->
    <script src="../js_ts/app-core.js"></script>
  </head>

  <body>

    <!-- Header Ultra-Moderne Style Professionnel -->
    <header class="ultra-modern-header">
      <div class="header-container">
        <!-- Section principale -->
        <div class="header-main-content">
          <!-- Logo à gauche -->
          <div class="header-left-section">
            <img src="/imgs/logo_cnc.png" alt="Logo DCOP 413" class="header-logo" />
            <h1 class="header-main-title">DCOP 413</h1>
          </div>

          <!-- Navigation à droite -->
          <div class="header-right-section">
            <div class="header-nav-buttons">
              <a href="/accueil" class="header-nav-btn" onclick="navigateWithTransition(event, '/accueil')">Accueil</a>
              <a href="/liste-visiteurs" class="header-nav-btn" onclick="navigateWithTransition(event, '/liste-visiteurs')">Visiteurs</a>
              <a href="/recherche" class="header-nav-btn" onclick="navigateWithTransition(event, '/recherche')">Recherche</a>
              <a href="/rapports" class="header-nav-btn" onclick="navigateWithTransition(event, '/rapports')">Rapports</a>
              <a href="/logout" class="header-nav-btn deconnexion">Déconnexion</a>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Section Photo Séparée -->
    <div class="photo-section-container">
      <div class="photo-section-wrapper">
        <div class="visitor-photo-container" id="headerPhotoContainer">
          <div class="visitor-photo-placeholder" id="photoPlaceholder">
            <span class="photo-icon">📷</span>
            <span class="photo-text">Photo du visiteur</span>
          </div>
          <img id="visitorPhoto" class="visitor-photo-preview" style="display: none;" />
        </div>

        <div class="photo-actions">
          <input id="photoFileInput" name="photo" type="file" accept="image/jpeg,image/png,image/webp" style="display: none;" />
          <button type="button" id="uploadPhotoBtn" class="photo-btn" title="Choisir une photo">
            📁 Choisir
          </button>
          <button type="button" id="capturePhotoBtn" class="photo-btn" title="Prendre un selfie">
            📸 Selfie
          </button>
          <button type="button" id="removePhotoBtn" class="photo-btn photo-btn-remove" style="display: none;" title="Supprimer la photo">
            🗑️ Supprimer
          </button>
        </div>

        <!-- Zone de capture vidéo (cachée par défaut) -->
        <div id="cameraSection" class="camera-section" style="display: none;">
          <video id="cameraVideo" class="camera-video" autoplay playsinline></video>
          <canvas id="cameraCanvas" style="display: none;"></canvas>
          <div class="camera-actions">
            <button type="button" id="takePictureBtn" class="photo-btn">
              📸 Capturer
            </button>
            <button type="button" id="cancelCameraBtn" class="photo-btn">
              ❌ Annuler
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Conteneur principal avec header -->
    <main role="main" class="modern-form-container-with-header">
      <div class="modern-form-wrapper">
        <form id="fiche-reception" class="ultra-form-sections" novalidate aria-label="Formulaire de réception des visiteurs">
          <!-- Section 1: Informations du Visiteur -->
          <div class="ultra-form-section">
            <div class="ultra-section-header">
              <h2 class="ultra-section-title">👤 Informations du Visiteur</h2>
              <p class="ultra-section-description">Veuillez renseigner les informations personnelles du visiteur avec précision pour assurer un accueil optimal.</p>
            </div>

            <div class="ultra-form-grid">
            <!-- Nom -->
              <div class="ultra-field-half">
                <div class="ultra-field-group">
                  <label for="nom" class="ultra-label ultra-label-required">Nom</label>
                  <input
                    type="text"
                    id="nom"
                    name="nom"
                    required
                    class="ultra-input"
                    placeholder="Entrez le nom de famille"
                    autocomplete="family-name"
                    maxlength="50"
                    pattern="[A-Za-zÀ-ÿ\s\-']{2,50}"
                  />
                </div>
              </div>

            <!-- Post-nom -->
            <div class="modern-field-half">
              <label for="postnom" class="modern-label">Post-nom</label>
              <div class="modern-input-wrapper">
                <input
                  type="text"
                  id="postnom"
                  name="postnom"
                  required
                  class="modern-input"
                  placeholder="Entrez le post-nom"
                  autocomplete="additional-name"
                  maxlength="50"
                  pattern="[A-Za-zÀ-ÿ\s\-']{2,50}"
                />
              </div>
            </div>

            <!-- Prénom -->
            <div class="modern-field-half">
              <label for="prenom" class="modern-label">Prénom</label>
              <div class="modern-input-wrapper">
                <input
                  type="text"
                  id="prenom"
                  name="prenom"
                  required
                  class="modern-input"
                  placeholder="Entrez le prénom"
                  autocomplete="given-name"
                  maxlength="50"
                  pattern="[A-Za-zÀ-ÿ\s\-']{2,50}"
                />
              </div>
            </div>

            <!-- Sexe -->
            <div class="modern-field-half">
              <label class="modern-label">Sexe</label>
              <div class="modern-input-wrapper">
                <div class="modern-select-wrapper">
                  <select id="sexe" name="sexe" required class="modern-select">
                    <option value="">Sélectionnez le sexe</option>
                    <option value="masculin">Masculin</option>
                    <option value="feminin">Féminin</option>
                  </select>
                  <svg class="modern-select-icon" viewBox="0 0 16 16" fill="currentColor">
                    <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>

            <!-- Organisation -->
            <div class="modern-field-two-thirds">
              <label for="organisation" class="modern-label">Organisation ou Entreprise</label>
              <div class="modern-input-wrapper">
                <input
                  type="text"
                  id="organisation"
                  name="organisation"
                  required
                  class="modern-input"
                  placeholder="Nom de l'organisation"
                  maxlength="100"
                />
              </div>
            </div>

            <!-- Fonction -->
            <div class="modern-field-third">
              <label for="fonction" class="modern-label">Fonction</label>
              <div class="modern-input-wrapper">
                <input
                  type="text"
                  id="fonction"
                  name="fonction"
                  required
                  class="modern-input"
                  placeholder="Fonction ou poste"
                  maxlength="100"
                />
              </div>
            </div>

            <!-- Téléphone 1 -->
            <div class="modern-field-third">
              <label for="telephone1" class="modern-label">Téléphone principal</label>
              <div class="modern-input-wrapper">
                <input
                  type="tel"
                  id="telephone1"
                  name="telephone1"
                  required
                  class="modern-input"
                  placeholder="+243 XXX XXX XXX"
                  pattern="[+]?[0-9\s\-\(\)]{8,20}"
                />
              </div>
            </div>

            <!-- Téléphone 2 -->
            <div class="modern-field-third">
              <label for="telephone2" class="modern-label">Téléphone secondaire</label>
              <div class="modern-input-wrapper">
                <input
                  type="tel"
                  id="telephone2"
                  name="telephone2"
                  class="modern-input"
                  placeholder="+243 XXX XXX XXX"
                  pattern="[+]?[0-9\s\-\(\)]{8,20}"
                />
              </div>
            </div>

            <!-- Email -->
            <div class="modern-field-two-thirds">
              <label for="email" class="modern-label">Adresse e-mail</label>
              <div class="modern-input-wrapper">
                <input
                  type="email"
                  id="email"
                  name="email"
                  required
                  class="modern-input"
                  placeholder="<EMAIL>"
                  autocomplete="email"
                />
              </div>
            </div>

            <!-- Champs complémentaires perfectionnés -->
            <div class="ultra-field-half">
              <div class="ultra-field-group">
                <label for="nationalite" class="ultra-label ultra-label-required">Nationalité</label>
                <select
                  id="nationalite"
                  name="nationalite"
                  required
                  class="ultra-input ultra-select"
                >
                  <option value="">Sélectionner la nationalité</option>
                  <option value="Congolaise">🇨🇩 Congolaise (RDC)</option>
                  <option value="Française">🇫🇷 Française</option>
                  <option value="Belge">🇧🇪 Belge</option>
                  <option value="Américaine">🇺🇸 Américaine</option>
                  <option value="Canadienne">🇨🇦 Canadienne</option>
                  <option value="Autre">🌍 Autre</option>
                </select>
              </div>
            </div>

            <div class="ultra-field-half">
              <div class="ultra-field-group">
                <label for="piece_identite" class="ultra-label ultra-label-required">Pièce d'identité</label>
                <select
                  id="piece_identite"
                  name="piece_identite"
                  required
                  class="ultra-input ultra-select"
                >
                  <option value="">Type de pièce d'identité</option>
                  <option value="Carte nationale">🆔 Carte nationale d'identité</option>
                  <option value="Passeport">📘 Passeport</option>
                  <option value="Permis de conduire">🚗 Permis de conduire</option>
                  <option value="Carte consulaire">🏛️ Carte consulaire</option>
                </select>
              </div>
            </div>

            <div class="ultra-field-half">
              <div class="ultra-field-group">
                <label for="numero_piece" class="ultra-label ultra-label-required">Numéro de la pièce</label>
                <input
                  type="text"
                  id="numero_piece"
                  name="numero_piece"
                  required
                  class="ultra-input"
                  placeholder="Numéro de la pièce d'identité"
                  maxlength="30"
                />
              </div>
            </div>

            <div class="ultra-field-half">
              <div class="ultra-field-group">
                <label for="date_visite" class="ultra-label ultra-label-required">Date de visite</label>
                <input
                  type="date"
                  id="date_visite"
                  name="date_visite"
                  required
                  class="ultra-input"
                  min=""
                />
              </div>
            </div>

            <div class="ultra-field-half">
              <div class="ultra-field-group">
                <label for="heure_arrivee" class="ultra-label ultra-label-required">Heure d'arrivée prévue</label>
                <input
                  type="time"
                  id="heure_arrivee"
                  name="heure_arrivee"
                  required
                  class="ultra-input"
                />
              </div>
            </div>

            <div class="ultra-field-half">
              <div class="ultra-field-group">
                <label for="personne_rencontrer" class="ultra-label ultra-label-required">Personne à rencontrer</label>
                <input
                  type="text"
                  id="personne_rencontrer"
                  name="personne_rencontrer"
                  required
                  class="ultra-input"
                  placeholder="Nom de la personne à rencontrer"
                  maxlength="100"
                />
              </div>
            </div>



          </div>
        </div>

        <!-- Section 2: Objectif de la Visite -->
        <div class="ultra-form-section">
          <div class="ultra-section-header">
            <h2 class="ultra-section-title">🎯 Objectif de la Visite</h2>
            <p class="ultra-section-description">Décrivez précisément le motif, la durée prévue et les détails de votre visite pour faciliter votre accueil.</p>
          </div>

          <div class="ultra-form-grid">
            <!-- Motif de la visite -->
            <div class="ultra-field-full">
              <div class="ultra-field-group">
                <label for="motif" class="ultra-label ultra-label-required">Motif détaillé de la visite</label>
                <textarea
                  id="motif"
                  name="motif"
                  required
                  class="ultra-input ultra-textarea"
                  placeholder="Décrivez précisément le motif de votre visite, la durée prévue, les documents à apporter..."
                  rows="4"
                ></textarea>

              </div>
            </div>
          </div>
        </div>

        <!-- Section 3: Consignes de Sécurité -->
        <div class="ultra-form-section">
          <div class="ultra-section-header">
            <h2 class="ultra-section-title">🔒 Consignes de Sécurité</h2>
            <p class="ultra-section-description">Nous vous informons des règles importantes à respecter lors de votre visite pour assurer la sécurité de tous.</p>
          </div>

          <div class="ultra-form-grid">
            <!-- Consignes de sécurité -->
            <div class="ultra-field-full">
              <div class="ultra-field-group">
                <label class="ultra-label ultra-label-required">Règles de sécurité obligatoires</label>
                <div class="ultra-security-rules">

                  <div class="ultra-security-rule">
                    <div class="ultra-security-icon">🏷️</div>
                    <div class="ultra-security-content">
                      <h4 class="ultra-security-title">Badge requis en permanence</h4>
                      <p class="ultra-security-description">Le port du badge visiteur est obligatoire durant toute la visite pour votre identification.</p>
                      <div class="ultra-security-checkbox">
                        <input id="badge-requis" name="consignes" type="checkbox" checked disabled class="ultra-checkbox-security" />
                        <label for="badge-requis" class="ultra-checkbox-label-security">J'ai pris connaissance</label>
                      </div>
                    </div>
                  </div>

                  <div class="ultra-security-rule">
                    <div class="ultra-security-icon">👥</div>
                    <div class="ultra-security-content">
                      <h4 class="ultra-security-title">Accompagnement obligatoire</h4>
                      <p class="ultra-security-description">Vous devez être accompagné par un agent autorisé en permanence lors de votre visite.</p>
                      <div class="ultra-security-checkbox">
                        <input id="accompagnement" name="consignes" type="checkbox" checked disabled class="ultra-checkbox-security" />
                        <label for="accompagnement" class="ultra-checkbox-label-security">J'ai pris connaissance</label>
                      </div>
                    </div>
                  </div>

                  <div class="ultra-security-rule">
                    <div class="ultra-security-icon">🚫</div>
                    <div class="ultra-security-content">
                      <h4 class="ultra-security-title">Respect des interdictions</h4>
                      <p class="ultra-security-description">Aucune captation audiovisuelle, usage d'appareils électroniques personnels interdit dans les zones sensibles.</p>
                      <div class="ultra-security-checkbox">
                        <input id="interdictions" name="consignes" type="checkbox" checked disabled class="ultra-checkbox-security" />
                        <label for="interdictions" class="ultra-checkbox-label-security">J'ai pris connaissance</label>
                      </div>
                    </div>
                  </div>

                </div>

              </div>
            </div>


          </div>
        </div>

        <!-- Champs cachés -->
        <input type="hidden" id="photoData" name="photoData" value="" />

        <!-- Boutons d'action ultra-modernes -->
        <div class="ultra-form-actions">
          <button type="button" class="ultra-btn ultra-btn-cancel" onclick="navigateWithTransition(event, '/accueil')">
            🏠 Retour à l'accueil
          </button>
          <button type="button" id="printBtn" class="ultra-btn ultra-btn-print">
            🖨️ Imprimer la fiche
          </button>
          <button type="submit" id="submitBtn" class="ultra-btn ultra-btn-submit">
            ✅ Enregistrer le visiteur
          </button>
        </div>

      </form>

      <!-- Footer d'impression perfectionné -->
      <div class="ultra-print-footer">
        <div class="ultra-print-header">
          <h1 class="ultra-print-title">DCOP 413</h1>
          <h2 class="ultra-print-subtitle">Fiche de Réception des Visiteurs</h2>
          <p class="ultra-print-department">Direction de la Coopération Opérationnelle</p>
        </div>

        <div class="ultra-print-info">
          <div class="ultra-print-row">
            <span class="ultra-print-label">Document :</span>
            <span class="ultra-print-value">Officiel - Confidentiel</span>
          </div>
          <div class="ultra-print-row">
            <span class="ultra-print-label">Date d'impression :</span>
            <span class="ultra-print-value" id="printDate"></span>
          </div>
          <div class="ultra-print-row">
            <span class="ultra-print-label">Référence :</span>
            <span class="ultra-print-value">DCOP-413-FRV-2025</span>
          </div>
        </div>

        <div class="ultra-print-signatures">
          <div class="ultra-signature-box">
            <p class="ultra-signature-title">Signature du Visiteur</p>
            <div class="ultra-signature-line"></div>
            <p class="ultra-signature-date">Date : _______________</p>
          </div>
          <div class="ultra-signature-box">
            <p class="ultra-signature-title">Signature de l'Agent d'Accueil</p>
            <div class="ultra-signature-line"></div>
            <p class="ultra-signature-date">Date : _______________</p>
          </div>
        </div>
      </div>

    </div>
  </main>

    <!-- Scripts unifiés déjà chargés dans le head -->

    <script>
      // Initialisation des champs du formulaire
      document.addEventListener('DOMContentLoaded', () => {
        // Initialiser la date d'aujourd'hui
        const dateVisiteField = document.getElementById('date_visite');
        if (dateVisiteField) {
          const today = new Date().toISOString().split('T')[0];
          dateVisiteField.value = today;
          dateVisiteField.min = today; // Empêcher les dates passées
        }

        // Initialiser l'heure actuelle
        const heureArriveeField = document.getElementById('heure_arrivee');
        if (heureArriveeField) {
          const now = new Date();
          const currentTime = now.getHours().toString().padStart(2, '0') + ':' +
                             now.getMinutes().toString().padStart(2, '0');
          heureArriveeField.value = currentTime;
        }

        // Animation d'entrée de la page
        document.body.style.opacity = '0';
        document.body.style.transform = 'translateY(20px)';
        document.body.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';

        setTimeout(() => {
          document.body.style.opacity = '1';
          document.body.style.transform = 'translateY(0)';
        }, 100);

        // Gestion des liens de navigation avec transition
        const navLinks = document.querySelectorAll('.header-nav-btn');
        navLinks.forEach(link => {
          link.addEventListener('click', (e) => {
            if (link.href && !link.href.includes('logout')) {
              e.preventDefault();

              // Animation de sortie
              document.body.style.transition = 'opacity 0.4s ease-out, transform 0.4s ease-out';
              document.body.style.opacity = '0.8';
              document.body.style.transform = 'translateY(-10px)';

              // Redirection après animation
              setTimeout(() => {
                window.location.href = link.href;
              }, 400);
            }
          });
        });
      });
    </script>

  </body>
</html>
