<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Capture Photo - DCOP 413</title>
  <meta name="description" content="Interface de capture photo pour le formulaire de réception des visiteurs">
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="../css/professional-design.css" />

  <!-- Styles -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="../css/unified-styles.css" />
  <link rel="stylesheet" href="../css/dynamic-styles.css" />

  <!-- Scripts -->
  <script src="../js_ts/navigation-config.js"></script>
  <script src="../js_ts/auth-guard.js"></script>
  <script src="../js_ts/app-core.js"></script>
  <script src="../js_ts/unified-app.js"></script>
</head>

<body class="modern-body">

  <!-- Header ultra-moderne pour page photo -->
  <header class="ultra-modern-header">
    <div class="header-container">
      <!-- Ligne du haut : Logo à gauche -->
      <div class="header-top-row">
        <!-- Logo en haut à gauche -->
        <div class="header-logo-section">
          <img src="/imgs/logo_cnc.png" alt="Logo DCOP 413" class="header-logo" />
        </div>
      </div>

      <!-- Ligne principale : Titre centré et navigation à droite -->
      <div class="header-main-content">
        <!-- Titre centré -->
        <div class="header-center-section">
          <h1 class="header-main-title">DCOP 413</h1>
        </div>

        <!-- Navigation à droite -->
        <div class="header-right-section">
          <div class="header-nav-buttons">
            <a href="accueil.html" class="header-nav-btn">Accueil</a>
            <a href="main.html" class="header-nav-btn">Formulaire</a>
            <a href="login.html" class="header-nav-btn">Connexion</a>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Conteneur principal avec header -->
  <div class="modern-form-container-with-header">
    <div class="modern-form-wrapper">

      <!-- Section de capture photo -->
      <div class="ultra-form-section">
        <div class="ultra-section-header">
          <h2 class="ultra-section-title">📷 Capture Photo</h2>
          <p class="ultra-section-description">Prenez votre photo d'identité pour le formulaire de réception des visiteurs</p>
        </div>

        <div class="ultra-form-grid">
          <!-- Instructions -->
          <div class="ultra-field-full">
            <div class="ultra-info-box">
              <h3 class="ultra-info-title">📋 Instructions pour le selfie :</h3>
              <ul class="ultra-info-list">
                <li>• Positionnez-vous face à la <strong>caméra frontale</strong> de votre appareil</li>
                <li>• Assurez-vous d'avoir un <strong>bon éclairage</strong> sur votre visage</li>
                <li>• Regardez directement la caméra et souriez 😊</li>
                <li>• Cliquez sur "📷 Prendre la photo" quand vous êtes prêt</li>
              </ul>
            </div>
          </div>

          <!-- Statut de la caméra -->
          <div class="ultra-field-full">
            <div id="cameraStatus" class="ultra-status-box">
              <div class="ultra-status-content">
                <div id="statusIndicator" class="ultra-status-indicator"></div>
                <span id="statusText" class="ultra-status-text">En attente d'autorisation...</span>
              </div>
            </div>
          </div>

          <!-- Vidéo -->
          <div class="ultra-field-full">
            <div class="ultra-video-container">
              <video
                id="video"
                autoplay
                playsinline
                muted
                class="ultra-video"
              ></video>
              <!-- Overlay pour guider l'utilisateur -->
              <div class="ultra-video-overlay">
                <div class="ultra-face-guide"></div>
                <div class="ultra-guide-text">
                  Positionnez votre visage dans le cercle
                </div>
              </div>
            </div>
          </div>

          <!-- Canvas caché -->
          <canvas id="canvas" class="hidden"></canvas>

          <!-- Boutons d'action -->
          <div class="ultra-field-full">
            <div class="ultra-form-actions">
              <button id="captureBtn" class="ultra-btn ultra-btn-submit">
                📷 Prendre la photo
              </button>
              <button onclick="handleCancel()" class="ultra-btn ultra-btn-cancel">
                ❌ Annuler
              </button>
            </div>
          </div>

          <!-- Zone de prévisualisation -->
          <div id="photoContainer" class="ultra-field-full hidden">
            <div class="ultra-success-message">
              <p class="ultra-success-text">✓ Photo capturée avec succès !</p>
            </div>

            <div class="ultra-photo-preview">
              <img
                id="capturedPhoto"
                alt="Votre selfie capturé"
                class="ultra-captured-photo"
              />
            </div>

            <div class="ultra-form-actions">
              <button onclick="usePhoto()" class="ultra-btn ultra-btn-submit">
                ✓ Utiliser cette photo
              </button>
              <button onclick="retakePhoto()" class="ultra-btn ultra-btn-print">
                🔄 Reprendre
              </button>
            </div>
          </div>

          <!-- Message d'erreur -->
          <div id="errorMsg" class="ultra-field-full hidden">
            <div class="ultra-error-message">
              <span id="errorText"></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  </main>


</body>
</html>
</create_file>
