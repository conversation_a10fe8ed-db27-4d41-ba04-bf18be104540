<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Capture Photo - DCOP 413</title>
  <meta name="description" content="Interface de capture photo pour le formulaire de réception des visiteurs">
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="../css/professional-design.css" />
</head>
<body class="photo-page">

  <main class="photo-container">
    <!-- Header -->
    <div class="photo-header">
      <h1 class="photo-title">Capture Photo</h1>
      <p class="photo-subtitle">DCOP 413 - Prenez votre photo d'identité</p>
    </div>

    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
      <h3 class="text-blue-800 font-semibold mb-2">📋 Instructions pour le selfie :</h3>
      <ul class="text-blue-700 text-sm space-y-1">
        <li>• Positionnez-vous face à la <strong>caméra frontale</strong> de votre appareil</li>
        <li>• Assurez-vous d'avoir un <strong>bon éclairage</strong> sur votre visage</li>
        <li>• Regardez directement la caméra et souriez 😊</li>
        <li>• Cliquez sur "📷 Prendre la photo" quand vous êtes prêt</li>
      </ul>
    </div>

    <!-- Statut de la caméra -->
    <div id="cameraStatus" class="mb-4 p-3 rounded-lg bg-gray-100 border border-gray-300">
      <div class="flex items-center gap-2">
        <div id="statusIndicator" class="w-3 h-3 rounded-full bg-gray-400"></div>
        <span id="statusText" class="text-gray-600 font-medium">En attente d'autorisation...</span>
      </div>
    </div>

    <!-- Vidéo -->
    <div class="relative rounded-lg overflow-hidden border-2 border-gray-300 shadow-lg">
      <video
        id="video"
        autoplay
        playsinline
        muted
        class="w-full h-auto aspect-video bg-black"
      ></video>
      <!-- Overlay pour guider l'utilisateur -->
      <div class="absolute inset-0 pointer-events-none">
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 border-2 border-white border-dashed rounded-full opacity-50"></div>
        <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-3 py-1 rounded text-sm">
          Positionnez votre visage dans le cercle
        </div>
      </div>
    </div>

    <!-- Canvas caché -->
    <canvas id="canvas" class="hidden"></canvas>

    <!-- Boutons -->
    <div class="flex flex-col sm:flex-row gap-4 justify-center">
      <button
        id="captureBtn"
        class="btn btn-primary"
      >
        📷 Prendre la photo
      </button>

      <button
        onclick="window.close()"
        class="btn btn-secondary"
      >
        ❌ Annuler
      </button>
    </div>

    <!-- Zone de prévisualisation -->
    <div id="photoContainer" class="hidden space-y-4">
      <p class="text-green-600 font-medium">✓ Photo capturée avec succès !</p>
      <div class="relative inline-block">
        <img
          id="capturedPhoto"
          alt="Votre selfie capturé"
          class="rounded-lg shadow-lg border-2 border-green-300 w-48 h-48 object-cover mx-auto"
        />
      </div>

      <div class="flex flex-col sm:flex-row gap-3 justify-center">
        <button
          onclick="usePhoto()"
          class="btn btn-success"
        >
          ✓ Utiliser cette photo
        </button>

        <button
          onclick="retakePhoto()"
          class="btn btn-warning"
        >
          🔄 Reprendre
        </button>
      </div>
    </div>

    <!-- Message d'erreur/succès -->
    <div id="errorMsg" class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg mt-4">
      <span id="errorText"></span>
    </div>
  </main>

  <!-- Script JS externe -->
  <script src="../js_ts/selfie.js" type="module" defer></script>
</body>
</html>
</create_file>
