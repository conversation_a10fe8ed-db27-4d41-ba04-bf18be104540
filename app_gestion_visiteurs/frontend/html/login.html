<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Connexion DCOP - 413 Gestion des visiteurs</title>
  <meta name="description" content="Interface de connexion professionnelle et sécurisée">

  <!-- Fonts Google -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

  <!-- Styles -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="../css/unified-styles.css" />

  <!-- Scripts -->
  <script src="../js_ts/unified-app.js"></script>
</head>

<body class="modern-body">

  <!-- Header ultra-moderne pour page login -->
  <header class="ultra-modern-header">
    <div class="header-container">
      <!-- Ligne du haut : Logo à gauche -->
      <div class="header-top-row">
        <!-- Logo en haut à gauche -->
        <div class="header-logo-section">
          <img src="/imgs/logo_cnc.png" alt="Logo DCOP 413" class="header-logo" />
        </div>
      </div>

      <!-- Ligne principale : Titre centré et navigation à droite -->
      <div class="header-main-content">
        <!-- Titre centré -->
        <div class="header-center-section">
          <h1 class="header-main-title">DCOP 413</h1>
        </div>

        <!-- Navigation à droite -->
        <div class="header-right-section">
          <div class="header-nav-buttons">
            <a href="accueil.html" class="header-nav-btn">Accueil</a>
            <a href="main.html" class="header-nav-btn">Formulaire</a>
            <a href="photo.html" class="header-nav-btn">Photo</a>
            <a href="#aide" class="header-nav-btn" onclick="alert('Fonctionnalité en développement')">Aide</a>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Conteneur principal avec header -->
  <div class="modern-form-container-with-header">
    <div class="modern-form-wrapper">

      <!-- Section de connexion -->
      <div class="ultra-form-section">
        <div class="ultra-section-header">
          <h2 class="ultra-section-title">🔑 Authentification</h2>
          <p class="ultra-section-description">Veuillez vous identifier pour accéder à l'application de gestion des visiteurs.</p>
        </div>

        <div class="ultra-form-grid">
          <!-- Formulaire de connexion -->
          <div class="ultra-field-full">
            <form id="loginForm" class="ultra-login-form" novalidate>

              <!-- Champ identifiant -->
              <div class="ultra-field-group">
                <label for="username" class="ultra-label ultra-label-required">Identifiant</label>
                <input
                  type="text"
                  id="username"
                  name="username"
                  required
                  class="ultra-input"
                  placeholder="Votre nom d'utilisateur"
                  autocomplete="username"
                />
              </div>

              <!-- Champ mot de passe -->
              <div class="ultra-field-group">
                <label for="password" class="ultra-label ultra-label-required">Mot de passe</label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  required
                  class="ultra-input"
                  placeholder="Votre mot de passe"
                  autocomplete="current-password"
                />
              </div>

              <!-- Options de connexion -->
              <div class="ultra-login-options">
                <div class="ultra-checkbox-container">
                  <input type="checkbox" id="remember" class="ultra-checkbox-beautiful" />
                  <label for="remember" class="ultra-checkbox-label-beautiful">
                    <div class="ultra-checkbox-icon">✓</div>
                  </label>
                  <span class="ultra-checkbox-text">Se souvenir de moi</span>
                </div>
                <a href="#" class="ultra-forgot-link" onclick="alert('Fonctionnalité en développement - Contactez l\'administrateur')">Mot de passe oublié ?</a>
              </div>

              <!-- Messages d'erreur et de succès -->
              <div id="errorMessage" class="ultra-message ultra-message-error hidden">
                <div class="ultra-message-icon">❌</div>
                <span id="errorText"></span>
              </div>

              <div id="successMessage" class="ultra-message ultra-message-success hidden">
                <div class="ultra-message-icon">✅</div>
                <span id="successText"></span>
              </div>

              <!-- Bouton de connexion -->
              <div class="ultra-form-actions">
                <button type="submit" id="loginButton" class="ultra-btn ultra-btn-submit ultra-btn-login">
                  🔐 Se connecter
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Scripts pour la gestion de la connexion -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const loginForm = document.getElementById('loginForm');
      const loginButton = document.getElementById('loginButton');
      const errorMessage = document.getElementById('errorMessage');
      const successMessage = document.getElementById('successMessage');
      const errorText = document.getElementById('errorText');
      const successText = document.getElementById('successText');

      if (loginForm) {
        loginForm.addEventListener('submit', async function(e) {
          e.preventDefault();

          const username = document.getElementById('username').value.trim();
          const password = document.getElementById('password').value.trim();

          // Masquer les messages précédents
          errorMessage.classList.add('hidden');
          successMessage.classList.add('hidden');

          // Validation côté client
          if (!username || !password) {
            errorText.textContent = 'Veuillez remplir tous les champs obligatoires';
            errorMessage.classList.remove('hidden');
            return;
          }

          // Désactiver le bouton pendant la connexion
          loginButton.disabled = true;
          loginButton.textContent = '🔄 Connexion...';

          try {
            // Utiliser AuthManager si disponible
            if (window.AuthManager) {
              const success = await window.AuthManager.login(username, password);
              if (success) {
                successText.textContent = 'Connexion réussie ! Redirection...';
                successMessage.classList.remove('hidden');
              } else {
                throw new Error('Échec de la connexion');
              }
            } else {
              // Fallback : simulation simple
              await new Promise(resolve => setTimeout(resolve, 1000));
              successText.textContent = 'Connexion réussie ! Redirection...';
              successMessage.classList.remove('hidden');

              setTimeout(() => {
                window.location.href = 'accueil.html';
              }, 1500);
            }
          } catch (error) {
            console.error('Erreur de connexion:', error);
            errorText.textContent = 'Identifiants incorrects. Veuillez réessayer.';
            errorMessage.classList.remove('hidden');
          } finally {
            // Réactiver le bouton
            loginButton.disabled = false;
            loginButton.textContent = '🔐 Se connecter';
          }
        });
      }
    });
  </script>

            </form>
          </div>
        </div>
      </div>

    </div>
  </div>



</body>
</html>
