<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Connexion DCOP - 413 Gestion des visiteurs</title>
  <meta name="description" content="Interface de connexion professionnelle et sécurisée">

  <!-- Fonts Google -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

  <!-- Styles -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="../css/professional-design.css" />

  <!-- Scripts -->
  <script src="../js_ts/navigation-manager.js"></script>
  <script src="../js_ts/app-core.js"></script>
</head>

<body class="modern-body">

  <!-- Header ultra-moderne pour page login -->
  <header class="ultra-modern-header">
    <div class="header-container">
      <!-- Ligne du haut : Logo à gauche -->
      <div class="header-top-row">
        <!-- Logo en haut à gauche -->
        <div class="header-logo-section">
          <img src="/imgs/logo_cnc.png" alt="Logo DCOP 413" class="header-logo" />
        </div>
      </div>

      <!-- Ligne principale : Titre centré, navigation et photo -->
      <div class="header-main-content">
        <!-- Titre centré -->
        <div class="header-center-section">
          <h1 class="header-main-title">DCOP 413</h1>
          <p class="header-subtitle">Système de Gestion des Visiteurs</p>
        </div>

        <!-- Section photo à droite -->
        <div class="header-photo-section">
          <div class="visitor-photo-container" id="headerPhotoContainer">
            <div class="photo-placeholder">
              <span class="photo-icon">👤</span>
              <span class="photo-text">Connexion</span>
            </div>
          </div>
        </div>

        <!-- Navigation à droite -->
        <div class="header-right-section">
          <div class="header-nav-buttons">
            <a href="accueil.html" class="header-nav-btn">🏠 Accueil</a>
            <a href="main.html" class="header-nav-btn">📝 Formulaire</a>
            <a href="#aide" class="header-nav-btn">❓ Aide</a>
            <a href="#contact" class="header-nav-btn">📞 Contact</a>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Conteneur principal avec header -->
  <main role="main" class="modern-form-container-with-header bg-gray-50 min-h-screen">
    <div class="modern-form-wrapper max-w-4xl mx-auto px-4 py-8">

      <!-- Section de connexion -->
      <div class="ultra-form-section bg-white rounded-xl shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300">
        <div class="ultra-section-header bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-xl p-6 border-b border-gray-200">
          <h2 class="ultra-section-title text-2xl font-bold text-gray-800 flex items-center gap-3">
            <span class="text-3xl">🔑</span>
            Authentification Sécurisée
          </h2>
          <p class="ultra-section-description text-gray-600 mt-2 leading-relaxed">
            Veuillez vous identifier pour accéder à l'application de gestion des visiteurs DCOP 413.
          </p>
        </div>

        <div class="p-6">
          <div class="ultra-form-grid max-w-md mx-auto space-y-6">
            <!-- Formulaire de connexion -->
            <form id="loginForm" class="ultra-login-form space-y-6" novalidate>

              <!-- Champ identifiant -->
              <div class="ultra-field-group space-y-2">
                <label for="username" class="ultra-label ultra-label-required block text-sm font-semibold text-gray-700 mb-2">
                  Identifiant
                  <span class="text-red-500 ml-1">*</span>
                </label>
                <input
                  type="text"
                  id="username"
                  name="username"
                  required
                  class="ultra-input w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                  placeholder="Votre nom d'utilisateur"
                  autocomplete="username"
                  aria-describedby="username-help"
                />
                <p id="username-help" class="text-xs text-gray-500 mt-1">Utilisez votre identifiant DCOP 413</p>
              </div>

              <!-- Champ mot de passe -->
              <div class="ultra-field-group space-y-2">
                <label for="password" class="ultra-label ultra-label-required block text-sm font-semibold text-gray-700 mb-2">
                  Mot de passe
                  <span class="text-red-500 ml-1">*</span>
                </label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  required
                  class="ultra-input w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                  placeholder="Votre mot de passe"
                  autocomplete="current-password"
                  aria-describedby="password-help"
                />
                <p id="password-help" class="text-xs text-gray-500 mt-1">Mot de passe sécurisé requis</p>
              </div>

              <!-- Options de connexion -->
              <div class="ultra-login-options flex items-center justify-between">
                <div class="ultra-checkbox-container flex items-center gap-2">
                  <input type="checkbox" id="remember" class="ultra-checkbox-beautiful w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" />
                  <label for="remember" class="text-sm text-gray-600 cursor-pointer">
                    Se souvenir de moi
                  </label>
                </div>
                <a href="#" class="ultra-forgot-link text-sm text-blue-600 hover:text-blue-800 transition-colors">
                  Mot de passe oublié ?
                </a>
              </div>

              <!-- Messages d'erreur et de succès -->
              <div id="errorMessage" class="ultra-message ultra-message-error hidden">
                <div class="bg-red-50 border border-red-200 rounded-lg p-3 flex items-center gap-2">
                  <div class="ultra-message-icon text-red-500">❌</div>
                  <span id="errorText" class="text-red-700 text-sm"></span>
                </div>
              </div>

              <div id="successMessage" class="ultra-message ultra-message-success hidden">
                <div class="bg-green-50 border border-green-200 rounded-lg p-3 flex items-center gap-2">
                  <div class="ultra-message-icon text-green-500">✅</div>
                  <span id="successText" class="text-green-700 text-sm"></span>
                </div>
              </div>

              <!-- Boutons d'action -->
              <div class="ultra-form-actions space-y-4">
                <button type="submit" id="loginButton" class="ultra-btn ultra-btn-submit w-full py-3 px-6 text-white font-semibold rounded-lg transition-all duration-200">
                  🔐 Se connecter
                </button>
                <div class="text-center">
                  <a href="accueil.html" class="text-sm text-gray-600 hover:text-blue-600 transition-colors">
                    ← Retour à l'accueil
                  </a>
                </div>
              </div>

            </form>
          </div>
        </div>
      </div>

    </div>
  </div>

  <!-- Script JavaScript -->
  <script src="/js_ts/login.js" defer></script>

</body>
</html>
