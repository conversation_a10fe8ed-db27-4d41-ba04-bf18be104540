<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Connexion DCOP - 413 Gestion des visiteurs</title>
  <meta name="description" content="Interface de connexion professionnelle et sécurisée">

  <!-- Fonts Google -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

  <!-- Styles -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="/css/professional-design.css" />

  <!-- Scripts -->
  <script src="/js_ts/app-core.js"></script>
</head>

<body class="modern-body">

  <!-- Header ultra-moderne pour page login -->
  <header class="login-header">
    <div class="header-container">
      <!-- Logo à gauche -->
      <div class="header-left-section">
        <img src="/home/<USER>/app_gestion_visiteurs/app_gestion_visiteurs/imgs/logo_cnc.png" alt="Logo DCOP 413" class="header-logo" />
      </div>

      <!-- Titre centré -->
      <div class="header-center-section">
        <h1 class="header-title">SYSTÈME DE GESTION DES VISITEURS - DCOP (413) </h1>
        <h2 class="header-subtitle inline-block" ></h2>
      </div>

    </div>
  </header>

  <!-- Conteneur principal avec header -->
  <div class="modern-form-container-with-header">
    <div class="modern-form-wrapper">

      <!-- Section de connexion -->
      <div class="ultra-form-section">
        <div class="ultra-section-header">
          <h2 class="ultra-section-title">🔑 Authentification</h2>
          <p class="ultra-section-description">Veuillez vous identifier pour accéder à l'application de gestion des visiteurs.</p>
        </div>

        <div class="ultra-form-grid">
          <!-- Formulaire de connexion -->
          <div class="ultra-field-full">
            <form id="loginForm" class="ultra-login-form" novalidate>

              <!-- Champ identifiant -->
              <div class="ultra-field-group">
                <label for="username" class="ultra-label ultra-label-required">Identifiant</label>
                <input
                  type="text"
                  id="username"
                  name="username"
                  required
                  class="ultra-input"
                  placeholder="Votre nom d'utilisateur"
                  autocomplete="username"
                />
              </div>

              <!-- Champ mot de passe -->
              <div class="ultra-field-group">
                <label for="password" class="ultra-label ultra-label-required">Mot de passe</label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  required
                  class="ultra-input"
                  placeholder="Votre mot de passe"
                  autocomplete="current-password"
                />
              </div>

              <!-- Options de connexion -->
              <div class="ultra-login-options">
                <div class="ultra-checkbox-container">
                  <input type="checkbox" id="remember" class="ultra-checkbox-beautiful" />
                  <label for="remember" class="ultra-checkbox-label-beautiful">
                    <div class="ultra-checkbox-icon">✓</div>
                  </label>
                  <span class="ultra-checkbox-text">Se souvenir de moi</span>
                </div>
                <a href="#" class="ultra-forgot-link">Mot de passe oublié ?</a>
              </div>

              <!-- Messages d'erreur et de succès -->
              <div id="errorMessage" class="ultra-message ultra-message-error hidden">
                <div class="ultra-message-icon">❌</div>
                <span id="errorText"></span>
              </div>

              <div id="successMessage" class="ultra-message ultra-message-success hidden">
                <div class="ultra-message-icon">✅</div>
                <span id="successText"></span>
              </div>

              <!-- Bouton de connexion -->
              <div class="ultra-form-actions">
                <button type="submit" id="loginButton" class="ultra-btn ultra-btn-submit ultra-btn-login">
                  🔐 Se connecter
                </button>
              </div>

            </form>
          </div>
        </div>
      </div>

    </div>
  </div>

  <!-- Script JavaScript -->
  <script src="/js_ts/login.js" defer></script>

</body>
</html>
